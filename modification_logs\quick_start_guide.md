# GuanBao AI 快速启动指南

## 🚀 快速启动

### 1. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python app.py
```
**服务地址**: http://************:82

### 2. 启动前端服务
```bash
cd vue-project
npm run dev
```
**服务地址**: http://localhost:5173

### 3. 访问应用
在浏览器中打开: http://localhost:5173

## 📱 登录测试

### 邀请用户白名单
以下手机号可以正常登录：
- `13900000000`
- `13913977533`
- `18888888888`
- `13800138000`

### 登录流程
1. 输入上述任一手机号
2. 点击"发送验证码"
3. 在开发环境中，验证码会显示在通知中
4. 输入验证码完成登录

## 🔧 服务依赖

### 必需服务
1. **vLLM模型服务**: http://************:81
   - 模型: GuanBao_BianCang_qwen2.5_7b_v0.0.1
   
2. **GuanBao后端服务**: http://************:82
   - 认证和聊天API

3. **前端服务**: http://localhost:5173
   - Vue 3 + Element Plus

### 服务检查
```bash
# 检查vLLM服务
curl http://************:81/v1/models

# 检查后端服务
curl http://************:82/api/health

# 检查前端服务
curl http://localhost:5173
```

## 🎯 功能测试

### 1. 登录功能
- [ ] 邀请用户可以正常登录
- [ ] 非邀请用户被拒绝
- [ ] 验证码倒计时正常
- [ ] 登录状态持久化

### 2. 聊天功能
- [ ] 服务连接状态正确显示
- [ ] 建议卡片可以点击
- [ ] 流式响应正常工作
- [ ] Markdown渲染正确

### 3. 用户管理
- [ ] 用户信息正确显示
- [ ] 登出功能正常
- [ ] 会话自动过期

## 🐛 常见问题

### 问题1: 前端无法连接后端
**现象**: 登录时提示"无法连接到服务器"
**解决**:
1. 确认后端服务正在运行: `python backend/app.py`
2. 检查端口82是否被占用
3. 确认防火墙设置

### 问题2: 后端无法连接vLLM
**现象**: 聊天时提示连接失败
**解决**:
1. 确认vLLM服务运行在************:81
2. 检查模型名称是否正确
3. 验证网络连接

### 问题3: 验证码收不到
**现象**: 点击发送验证码无响应
**解决**:
1. 检查手机号是否在白名单中
2. 查看浏览器控制台错误
3. 确认后端服务正常

### 问题4: 登录后无法聊天
**现象**: 登录成功但聊天功能不可用
**解决**:
1. 检查API密钥配置
2. 确认用户权限
3. 查看后端日志

## 📊 开发调试

### 浏览器控制台
按F12打开开发者工具，查看：
- **Console**: 错误日志和调试信息
- **Network**: API请求和响应
- **Application**: 本地存储的用户信息

### 后端日志
后端服务会输出详细日志：
```
INFO:__name__:为手机号 13900000000 生成验证码: 123456
INFO:__name__:用户 13900000000 登录成功
INFO:__name__:正在处理请求: 1234567890
```

### 前端调试
在浏览器控制台执行：
```javascript
// 查看当前用户信息
console.log(localStorage.getItem('userInfo'));

// 查看API配置
console.log('API配置:', window.API_CONFIG);
```

## 🔒 安全注意事项

### 开发环境
- 验证码会显示在前端通知中
- API密钥硬编码在代码中
- 用户白名单在代码中配置

### 生产环境建议
- 集成真实短信服务
- 使用环境变量管理密钥
- 将用户白名单存储在数据库
- 实施API访问频率限制
- 添加日志监控和告警

## 📞 技术支持

### 项目结构
```
GuanBao/
├── backend/           # 后端服务
│   ├── app.py        # 主服务文件
│   └── requirements.txt
├── vue-project/      # 前端项目
│   └── src/
│       ├── components/
│       └── utils/
└── modification_logs/ # 开发文档
```

### 联系方式
如遇技术问题，请查看：
1. `modification_logs/` 目录下的详细文档
2. 浏览器控制台的错误信息
3. 后端服务的日志输出

---

**现在可以开始使用GuanBao AI了！** 🎉

**测试步骤**:
1. 访问 http://localhost:5173
2. 使用白名单手机号登录
3. 开始AI对话体验
