# 项目架构文档

## 1. 项目概述
本项目是一个基于 Vue 3 和 Element Plus 的大语言模型（LLM）聊天前端，旨在提供现代化、交互友好的用户界面。前端完全遵循 OpenAI 的 API 数据协议，支持流式响应和多轮对话。

## 2. 技术栈
- **框架**: Vue 3 (Composition API with `<script setup>`)
- **UI 组件库**: Element Plus
- **Markdown 解析**: marked
- **代码高亮**: highlight.js
- **HTTP 通信**: 原生 fetch API

## 3. 核心功能
- **现代化双栏布局**: 左侧为会话历史列表，右侧为核心聊天区。
- **流式响应**: 实时显示模型生成的每一个字（打字机效果）。
- **上下文对话**: 支持多轮对话，自动管理和发送完整的聊天上下文。
- **Markdown 渲染**: 解析并显示模型返回的 Markdown 格式内容。
- **代码高亮与复制**: 自动识别并高亮代码块，提供一键复制功能。
- **欢迎界面**: 新对话开始时展示欢迎页面和功能提示卡片。
- **一键清空对话**: 方便用户随时开启新会话。

## 4. 页面布局
### 4.1 左侧边栏 (`<el-aside>`)
- **功能**: 会话管理。
- **顶部**: “新建对话”按钮，清空聊天记录并重置为欢迎界面。
- **中部**: 会话历史列表（预留布局）。

### 4.2 右侧主聊天区 (`<el-main>`)
- **欢迎界面**: 无聊天记录时显示，包含 Logo、标题和功能建议卡片。
- **聊天记录区**: 使用 `el-scrollbar` 组件，支持平滑滚动。
- **消息渲染**: 区分用户和机器人消息，支持 Markdown 解析和代码高亮。
- **底部输入区**: 包含输入框和发送按钮，支持自动高度调整。

## 5. 核心实现
### 5.1 状态管理
使用 Vue 3 的 Composition API 管理以下状态：
- `chatHistory`: 存储对话消息数组。
- `isLoading`: 控制发送按钮的禁用状态。
- `messageInput`: 绑定输入框内容。

### 5.2 API 通信（流式处理）
1. 构造符合 OpenAI 格式的请求体（`stream: true`）。
2. 使用 `fetch API` 发起 POST 请求。
3. 通过 `response.body.getReader()` 读取流式数据。
4. 解析增量内容并实时更新界面。

### 5.3 Markdown 与代码高亮
1. 实时渲染流式内容为 HTML。
2. 流结束后进行完整 Markdown 渲染。
3. 为代码块添加“复制”按钮，支持一键复制。

## 6. 后续建议
- 实现会话历史列表功能。
- 优化流式响应的性能。
- 扩展 Markdown 支持的功能（如表格、图片等）。