# GuanBao AI Chat 配置管理

## 概述

本目录包含GuanBao AI Chat项目的统一配置管理系统，支持前端和后端的配置统一管理。

## 文件结构

```
config/
├── app.config.json     # 主配置文件
├── config.py          # Python配置加载器
├── config.js          # JavaScript配置加载器
├── config.d.ts        # TypeScript类型定义
└── README.md          # 本文档
```

## 配置文件说明

### app.config.json

主配置文件，包含所有配置项：

```json
{
  "project": {
    "name": "GuanBao AI Chat",
    "version": "1.0.0",
    "description": "基于Vue 3和FastAPI的AI聊天应用"
  },
  "frontend": {
    "host": "localhost",
    "port": 5173
  },
  "backend": {
    "host": "0.0.0.0",
    "port": 82
  },
  "api": {
    "base_url": "http://localhost:82",
    "endpoints": {
      "health": "/api/health",
      "chat_completions": "/v1/chat/completions"
    }
  }
}
```

## 使用方法

### Python后端使用

```python
from config.config import get_config

# 获取配置实例
config = get_config()

# 使用配置
host = config.backend_host
port = config.backend_port
api_key = config.api_key

# 启动服务器
server_address = (host, port)
```

### JavaScript/TypeScript前端使用

```javascript
import config from '../config/config.js';

// 获取配置
const frontendHost = config.frontendHost;
const frontendPort = config.frontendPort;
const apiBaseUrl = config.apiBaseUrl;

// 获取API URL
const healthUrl = config.getApiUrl('health');
```

### Vite配置使用

```typescript
import { defineConfig } from 'vite'
import fs from 'fs'
import path from 'path'

// 加载配置
const configPath = path.resolve(__dirname, '../config/app.config.json')
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))

export default defineConfig({
  server: {
    host: config.frontend.host,
    port: config.frontend.port,
    proxy: {
      '/api': {
        target: `http://${config.backend.host}:${config.backend.port}`,
        changeOrigin: true
      }
    }
  }
})
```

## 环境变量覆盖

支持通过环境变量覆盖配置文件中的设置：

### 前端配置
- `FRONTEND_HOST`: 前端主机地址
- `FRONTEND_PORT`: 前端端口

### 后端配置
- `BACKEND_HOST`: 后端主机地址
- `BACKEND_PORT`: 后端端口

### API配置
- `API_BASE_URL`: API基础URL

### 安全配置
- `SECRET_KEY`: 密钥
- `API_KEY`: API密钥

### 使用示例

```bash
# 设置环境变量
export FRONTEND_PORT=3000
export BACKEND_PORT=8080
export API_BASE_URL=http://localhost:8080

# 启动服务
npm run dev
python backend/simple_server.py
```

## 配置项说明

### 项目配置 (project)
- `name`: 项目名称
- `version`: 项目版本
- `description`: 项目描述

### 前端配置 (frontend)
- `host`: 前端服务器主机地址
- `port`: 前端服务器端口
- `dev.open`: 开发时是否自动打开浏览器
- `dev.cors`: 是否启用CORS
- `build.outDir`: 构建输出目录
- `build.assetsDir`: 静态资源目录

### 后端配置 (backend)
- `host`: 后端服务器主机地址
- `port`: 后端服务器端口
- `dev.debug`: 开发模式是否启用调试
- `dev.reload`: 开发模式是否启用热重载
- `prod.workers`: 生产模式工作进程数

### API配置 (api)
- `base_url`: API基础URL
- `endpoints`: API端点映射
- `timeout`: 请求超时时间
- `retry_attempts`: 重试次数

### 数据库配置 (database)
- `type`: 数据库类型
- `url`: 数据库连接URL
- `pool_size`: 连接池大小
- `max_overflow`: 最大溢出连接数

### 安全配置 (security)
- `secret_key`: 应用密钥
- `api_key`: API密钥
- `session_timeout`: 会话超时时间
- `cors_origins`: CORS允许的源

### 日志配置 (logging)
- `level`: 日志级别
- `format`: 日志格式
- `file`: 日志文件路径
- `max_size`: 日志文件最大大小
- `backup_count`: 日志文件备份数量

### 功能配置 (features)
- `registration_enabled`: 是否启用注册
- `invited_users_only`: 是否仅限邀请用户
- `max_sessions_per_user`: 每用户最大会话数
- `max_message_length`: 最大消息长度
- `stream_response`: 是否启用流式响应

## 最佳实践

### 1. 开发环境
- 使用默认配置文件
- 通过环境变量覆盖特定设置
- 前端使用代理转发API请求

### 2. 生产环境
- 修改配置文件中的生产环境设置
- 使用环境变量管理敏感信息
- 配置适当的CORS策略

### 3. 配置管理
- 不要在代码中硬编码配置
- 使用配置加载器统一管理
- 为不同环境准备不同的配置文件

## 故障排除

### 配置文件不存在
```
FileNotFoundError: 配置文件不存在: config/app.config.json
```
**解决方案**: 确保配置文件存在且路径正确

### 配置文件格式错误
```
ValueError: 配置文件格式错误: Expecting ',' delimiter
```
**解决方案**: 检查JSON格式是否正确

### 端口冲突
```
OSError: [Errno 48] Address already in use
```
**解决方案**: 修改配置文件中的端口或停止占用端口的进程

### 代理配置错误
```
Error: connect ECONNREFUSED 127.0.0.1:82
```
**解决方案**: 确保后端服务正在运行且端口配置正确

## 更新日志

### v1.0.0 (2025-08-07)
- 初始版本
- 支持前后端统一配置
- 支持环境变量覆盖
- 提供Python和JavaScript配置加载器
