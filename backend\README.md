# GuanBao 后端服务

## 功能说明
- 手机号验证码登录
- 内部邀请用户白名单验证
- 流式聊天API
- 与vLLM模型集成

## 安装依赖
```bash
pip install -r requirements.txt
```

## 启动服务
```bash
python app.py
```

服务将在 http://0.0.0.0:82 启动

## API接口

### 1. 发送验证码
- **URL**: `/api/send-verification-code`
- **方法**: POST
- **参数**: 
  ```json
  {
    "phone_number": "13900000000"
  }
  ```

### 2. 验证登录
- **URL**: `/api/verify-login`
- **方法**: POST
- **参数**:
  ```json
  {
    "phone_number": "13900000000",
    "verification_code": "123456"
  }
  ```

### 3. 聊天接口
- **URL**: `/v1/chat/completions`
- **方法**: POST
- **参数**:
  ```json
  {
    "request_id": "12345",
    "phone_number": "13900000000",
    "query": "你好",
    "api_key": "GuanBao_2024_API_KEY"
  }
  ```

## 配置说明
- 邀请用户白名单在 `INVITED_PHONES` 中配置
- API密钥在 `API_KEY` 中配置
- vLLM服务地址在 `chat_model` 初始化中配置
