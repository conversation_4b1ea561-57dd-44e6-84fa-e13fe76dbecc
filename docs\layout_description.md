# 页面布局自然语言描述 (1920×1080分辨率)

## 整体布局
页面采用左右双栏设计，总宽度为1920px，高度为1080px。左侧边栏固定宽度为320px，右侧主区域宽度为1600px，两者之间有1px的浅色分隔线。

## 左侧边栏 (320px宽)
1. 顶部区域 (高度80px):
   - "新建对话"按钮: 宽度280px，高度40px，居中显示，距离顶部20px
   - 按钮样式: 蓝色渐变背景，白色文字，圆角8px
2. 会话历史区 (高度1000px):
   - 距离顶部100px，内边距16px
   - 每个会话项高度60px，包含时间戳和首句预览
   - 滚动条宽度6px，采用半透明设计

## 右侧主区域 (1600px宽)
1. 欢迎界面 (居中显示):
   - Logo: 200px×200px，距离顶部150px
   - 标题: 字号32px，下方间距40px
   - 功能卡片: 4张卡片，每张尺寸360px×180px，间距20px

2. 聊天记录区 (高度800px):
   - 消息气泡最大宽度1000px
   - 用户消息: 靠右对齐，距离右侧边界100px
   - AI消息: 靠左对齐，距离左侧边界100px
   - 消息间距: 垂直间距24px

3. 输入区 (固定高度200px):
   - 文本框: 宽度1400px，最小高度80px，最大高度160px
   - 发送按钮: 宽度120px，高度40px，距离文本框左侧20px