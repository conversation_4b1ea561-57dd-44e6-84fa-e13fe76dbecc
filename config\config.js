/**
 * GuanBao AI Chat 前端配置管理
 * 统一管理前端配置信息
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FrontendConfig {
  constructor(configFile = null) {
    if (!configFile) {
      // 默认配置文件路径
      configFile = path.join(__dirname, 'app.config.json');
    }
    
    this.configFile = configFile;
    this._config = this._loadConfig();
  }

  _loadConfig() {
    try {
      const configData = fs.readFileSync(this.configFile, 'utf8');
      const config = JSON.parse(configData);
      
      // 应用环境变量覆盖
      this._applyEnvOverrides(config);
      
      return config;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`配置文件不存在: ${this.configFile}`);
      } else if (error instanceof SyntaxError) {
        throw new Error(`配置文件格式错误: ${error.message}`);
      }
      throw error;
    }
  }

  _applyEnvOverrides(config) {
    // 前端配置环境变量覆盖
    if (process.env.FRONTEND_HOST) {
      config.frontend.host = process.env.FRONTEND_HOST;
    }
    if (process.env.FRONTEND_PORT) {
      config.frontend.port = parseInt(process.env.FRONTEND_PORT);
    }
    
    // 后端配置环境变量覆盖
    if (process.env.BACKEND_HOST) {
      config.backend.host = process.env.BACKEND_HOST;
    }
    if (process.env.BACKEND_PORT) {
      config.backend.port = parseInt(process.env.BACKEND_PORT);
    }
    
    // API配置环境变量覆盖
    if (process.env.API_BASE_URL) {
      config.api.base_url = process.env.API_BASE_URL;
    }
  }

  get(key, defaultValue = null) {
    const keys = key.split('.');
    let value = this._config;
    
    try {
      for (const k of keys) {
        value = value[k];
      }
      return value;
    } catch (error) {
      return defaultValue;
    }
  }

  set(key, value) {
    const keys = key.split('.');
    let config = this._config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in config)) {
        config[k] = {};
      }
      config = config[k];
    }
    
    config[keys[keys.length - 1]] = value;
  }

  save() {
    fs.writeFileSync(this.configFile, JSON.stringify(this._config, null, 2), 'utf8');
  }

  // 便捷属性访问
  get frontendHost() {
    return this.get('frontend.host', 'localhost');
  }

  get frontendPort() {
    return this.get('frontend.port', 5173);
  }

  get backendHost() {
    return this.get('backend.host', 'localhost');
  }

  get backendPort() {
    return this.get('backend.port', 82);
  }

  get apiBaseUrl() {
    return this.get('api.base_url', `http://${this.backendHost}:${this.backendPort}`);
  }

  get apiEndpoints() {
    return this.get('api.endpoints', {});
  }

  get corsOrigins() {
    return this.get('security.cors_origins', [`http://${this.frontendHost}:${this.frontendPort}`]);
  }

  // 获取完整的API URL
  getApiUrl(endpoint) {
    const endpoints = this.apiEndpoints;
    const endpointPath = endpoints[endpoint] || endpoint;
    return `${this.apiBaseUrl}${endpointPath}`;
  }

  // 获取前端开发服务器配置
  getDevServerConfig() {
    return {
      host: this.frontendHost,
      port: this.frontendPort,
      open: this.get('frontend.dev.open', true),
      cors: this.get('frontend.dev.cors', true),
      proxy: {
        '/api': {
          target: this.apiBaseUrl,
          changeOrigin: true,
          secure: false
        },
        '/v1': {
          target: this.apiBaseUrl,
          changeOrigin: true,
          secure: false
        }
      }
    };
  }

  // 获取构建配置
  getBuildConfig() {
    return {
      outDir: this.get('frontend.build.outDir', 'dist'),
      assetsDir: this.get('frontend.build.assetsDir', 'assets')
    };
  }

  toString() {
    return `FrontendConfig(frontend=${this.frontendHost}:${this.frontendPort}, backend=${this.backendHost}:${this.backendPort})`;
  }
}

// 创建全局配置实例
const config = new FrontendConfig();

// 导出配置实例和类
export { FrontendConfig, config };
export default config;

// CommonJS 兼容
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { FrontendConfig, config };
  module.exports.default = config;
}
