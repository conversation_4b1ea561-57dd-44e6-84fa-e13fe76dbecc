# n8n 节点分类索引

## 快速导航

本文档提供了n8n所有节点的分类索引，方便快速查找所需的节点类型。

## 1. 核心节点（Core Nodes）

### 触发器节点（Trigger Nodes）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| Manual Trigger | 手动触发工作流 | 测试、手动执行 |
| Schedule Trigger | 定时触发工作流 | 定期任务、批处理 |
| Webhook | HTTP请求触发 | API集成、外部系统调用 |
| Email Trigger (IMAP) | 邮件接收触发 | 邮件处理、客服自动化 |
| RSS Feed Trigger | RSS更新触发 | 内容监控、新闻聚合 |
| Local File Trigger | 文件变化触发 | 文件监控、数据处理 |
| Error Trigger | 错误事件触发 | 错误处理、故障恢复 |
| Activation Trigger | 工作流激活触发 | 初始化任务 |
| n8n Trigger | n8n内部事件触发 | 系统集成 |
| Workflow Trigger | 其他工作流完成触发 | 工作流链式调用 |
| SSE Trigger | 服务器推送事件触发 | 实时数据接收 |
| Chat Trigger | 聊天消息触发 | 聊天机器人、客服 |
| n8n Form Trigger | 表单提交触发 | 数据收集、用户反馈 |

### 数据处理节点（Data Processing）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| Code | JavaScript/Python代码执行 | 复杂逻辑、数据转换 |
| Filter | 数据过滤 | 条件筛选、数据清洗 |
| If | 条件判断分支 | 简单条件逻辑 |
| Switch | 多条件分支路由 | 复杂分支逻辑 |
| Merge | 数据流合并 | 数据汇总、结果合并 |
| Sort | 数据排序 | 数据整理、排名 |
| Limit | 限制数据数量 | 分页、数据截取 |
| Aggregate | 数据聚合 | 统计计算、数据汇总 |
| Compare Datasets | 数据集比较 | 差异分析、数据对比 |
| Remove Duplicates | 去除重复数据 | 数据清洗、去重 |
| Split Out | 数组拆分 | 数据展开、批处理 |
| Loop Over Items | 批量循环处理 | 大数据处理、批量操作 |
| Edit Fields (Set) | 字段编辑设置 | 数据格式化、字段操作 |
| Rename Keys | 重命名对象键 | 数据结构调整 |

### 文件处理节点（File Processing）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| Read/Write Files from Disk | 本地文件读写 | 文件操作、数据导入导出 |
| Extract From File | 文件内容提取 | 文档解析、数据提取 |
| Convert to File | 数据转文件 | 报告生成、文件创建 |
| Compression | 文件压缩解压 | 文件打包、存储优化 |
| Edit Image | 图像编辑 | 图片处理、缩略图生成 |
| HTML | HTML内容处理 | 网页解析、内容提取 |
| XML | XML数据处理 | 数据交换、配置解析 |
| Markdown | Markdown文档处理 | 文档转换、内容格式化 |

### 网络通信节点（Network Communication）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| HTTP Request | HTTP请求发送 | API调用、数据获取 |
| GraphQL | GraphQL查询 | 现代API查询 |
| FTP | FTP文件传输 | 文件上传下载 |
| SSH | SSH远程执行 | 服务器管理、远程操作 |
| LDAP | LDAP目录查询 | 用户认证、目录服务 |
| Send Email | 邮件发送 | 通知、营销邮件 |
| Respond to Webhook | Webhook响应 | API服务、数据返回 |
| Respond to Chat | 聊天响应 | 聊天机器人、自动回复 |

### 工作流控制节点（Workflow Control）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| Wait | 暂停等待 | 延时执行、等待条件 |
| Stop And Error | 停止并报错 | 错误处理、流程中断 |
| Execute Sub-workflow | 执行子工作流 | 模块化、代码复用 |
| Execute Command | 系统命令执行 | 系统操作、脚本运行 |
| No Operation | 空操作 | 占位符、调试 |
| Debug Helper | 调试助手 | 问题诊断、数据查看 |
| Execution Data | 执行数据获取 | 元数据访问、状态查询 |

### AI和机器学习节点（AI & ML）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| AI Transform | AI数据转换 | 智能数据处理 |
| Summarize | 文本摘要 | 内容总结、信息提取 |
| Evaluation | AI模型评估 | 模型测试、性能评估 |

### 实用工具节点（Utilities）
| 节点名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| Date & Time | 日期时间处理 | 时间计算、格式转换 |
| Crypto | 加密解密 | 数据安全、密码处理 |
| JWT | JWT令牌处理 | 身份验证、令牌管理 |
| TOTP | 双因素认证 | 安全验证、OTP生成 |
| n8n | n8n系统管理 | 系统操作、配置管理 |
| n8n Form | 表单创建 | 数据收集、用户输入 |
| RSS Read | RSS内容读取 | 内容聚合、信息获取 |
| Git | 版本控制操作 | 代码管理、版本控制 |

## 2. 应用节点（App Nodes）按行业分类

### 云服务平台
**Amazon Web Services (AWS)**
- Certificate Manager, Cognito, Comprehend, DynamoDB
- Elastic Load Balancing, Lambda, Rekognition, S3
- SES, SNS, SQS, Textract, Transcribe

**Microsoft Azure**
- Cosmos DB, Storage

**Google Cloud Platform**
- Firestore, Natural Language, Realtime Database, Storage

### 办公协作套件
**Google Workspace**
- Calendar, Chat, Contacts, Docs, Drive
- Sheets, Slides, Tasks, Translate, Admin
- Ads, Analytics, BigQuery, Books, Business Profile

**Microsoft 365**
- Dynamics CRM, Entra ID, Excel 365, Graph Security
- OneDrive, Outlook, SharePoint, Teams, To Do, SQL

### 项目管理工具
- Asana, ClickUp, Jira Software, Linear
- monday.com, Notion, Trello, Todoist
- Airtable, Baserow, Coda

### 客户关系管理
- HubSpot, Salesforce, Pipedrive, Freshworks CRM
- Copper, Agile CRM, Affinity, Monica CRM, Salesmate

### 营销自动化平台
- Mailchimp, ActiveCampaign, ConvertKit, GetResponse
- Brevo, Customer.io, Iterable, Mailgun, Mailjet
- SendGrid, Mandrill, MailerLite, Automizy

### 电商平台
- Shopify, WooCommerce, Magento 2
- Stripe, PayPal, Chargebee, Paddle

### 社交媒体平台
- Facebook Graph API, X (Twitter), LinkedIn
- Discord, Telegram, WhatsApp Business Cloud
- Line, Matrix, Mattermost, Slack, Rocket.Chat

### 开发工具
- GitHub, GitLab, Jenkins, CircleCI, Travis CI, npm

### 数据库系统
- MySQL, PostgreSQL, MongoDB, Redis
- Elasticsearch, InfluxDB, CrateDB, QuestDB
- TimescaleDB, Snowflake, Supabase, NocoDB

### 人工智能服务
- OpenAI, Anthropic, Google Gemini, Hugging Face
- Mistral AI, Ollama, Perplexity, xAI, OpenRouter

### 通讯消息服务
- Twilio, Vonage, MessageBird, Plivo
- MSG91, Mocean, seven, Gotify
- Pushbullet, Pushover, Pushcut, SIGNL4

### 文件存储管理
- Dropbox, Box, Nextcloud, Webflow
- Contentful, Strapi, Ghost, WordPress, Storyblok

### 监控分析工具
- Grafana, PostHog, Metabase, Sentry.io
- UptimeRobot, PagerDuty

### 安全工具
- VirusTotal, urlscan.io, MISP, TheHive
- Cortex, OpenCTI, Recorded Future, QRadar
- Splunk, Elastic Security, Qualys

### 金融会计软件
- QuickBooks, Xero, Invoice Ninja, ProfitWell
- Wise, CoinGecko, marketstack

### 人力资源工具
- BambooHR, Workable, Harvest, Toggl, Clockify

### 客户支持平台
- Freshdesk, Freshservice, Help Scout, Intercom
- Zendesk, ServiceNow

### 调研表单工具
- Typeform, SurveyMonkey, JotForm, Wufoo, KoboToolbox

### 网络基础设施
- Cloudflare, Netlify, Home Assistant, Philips Hue
- MQTT, SSH

## 3. 社区节点（Community Nodes）

### 特点
- 社区开发维护
- 功能扩展丰富
- 更新迭代快速
- 需要安全评估

### 安装方式
- n8n界面安装（推荐）
- npm/yarn手动安装
- 验证节点优先选择

### 注意事项
- 安全风险评估
- 兼容性检查
- 维护状态确认
- 依赖管理

## 使用建议

### 节点选择原则
1. **功能匹配** - 选择最符合需求的节点
2. **稳定性优先** - 优先使用官方内置节点
3. **性能考虑** - 评估节点的性能影响
4. **安全评估** - 特别是第三方节点的安全性

### 常见组合模式
- **数据处理链** - Filter → Code → Set → Output
- **API集成流** - Trigger → HTTP Request → Process → Store
- **监控告警流** - Schedule → Check → If → Notify
- **文件处理流** - File Trigger → Extract → Transform → Save

### 最佳实践
- 合理使用错误处理节点
- 添加调试和日志节点
- 模块化设计复杂工作流
- 定期测试和维护工作流

这个索引文档可以帮助您快速找到所需的节点类型，并了解其主要用途和适用场景。
