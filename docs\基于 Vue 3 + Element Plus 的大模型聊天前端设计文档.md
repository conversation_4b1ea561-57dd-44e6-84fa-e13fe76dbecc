1. 项目概述
本文档旨在设计并实现一个现代化、交互友好的大语言模型（LLM）聊天前端。该前端使用 Vue 3 作为核心框架，并借助 Element Plus 组件库快速构建美观的 UI 界面。
此前端将完全遵循 OpenAI 的 API 数据协议，能够与任何兼容该协议的后端服务（如自建的 FastAPI/vLLM 服务或官方 API）进行无缝通信，并支持完整的流式响应（打字机效果）。
技术栈:
- 框架: Vue 3 (Composition API with <script setup>)
- UI 组件库: Element Plus
- Markdown 解析: marked
- 代码高亮: highlight.js
- HTTP 通信: 原生 fetch API
2. 核心功能特性
- ✨ 现代化双栏布局: 左侧为会话历史列表，右侧为核心聊天区，布局清晰，符合主流AI助手应用的设计。
- 🚀 流式响应 (打字机效果): 实时显示模型生成的每一个字，极大提升用户等待体验。
- 🤖 上下文对话: 支持多轮对话，前端会自动管理和发送完整的聊天上下文。
- 🎨 Markdown 渲染: 完美解析模型返回的 Markdown 格式内容，支持标题、列表、粗体、链接等。
- 💻 代码高亮与复制: 自动识别并高亮显示代码块，并为每个代码块提供一键“复制”功能。
- 👋 欢迎界面: 在新对话开始前，展示一个友好的欢迎页面和功能提示卡片，引导用户使用。
- 🗑️ 一键清空对话: 方便用户随时开启一个全新的会话。
3. 页面布局设计
页面采用经典的左右双栏布局，通过 Element Plus 的 el-container 组件实现。
3.1. 左侧边栏 (<el-aside>)
- 功能: 主要用于会话管理。
- 顶部: 一个醒目的 “新建对话” 按钮，点击后会清空右侧的聊天记录，并重置为欢迎界面。
- 中部: 一个可滚动的会话历史列表。此处为功能扩展区，当前设计中暂未实现具体历史列表，但已预留好布局位置。
- 设计: 背景色稍深，与主内容区形成视觉区分，宽度固定。
3.2. 右侧主聊天区 (<el-main>)
这是用户交互的核心区域，它自身也分为三个部分：
- 欢迎界面 (v-if):
  - 触发条件: 当没有聊天记录时显示。
  - 内容:
    - 一个醒目的 Logo + 应用标题。
    - 四张功能建议卡片（使用 el-card），每张卡片包含一个标题和一个示例问题。点击卡片，其上的问题会自动填充到输入框并发送。
- 聊天记录区 (v-else):
  - 容器: 使用 el-scrollbar 组件，确保在内容溢出时可以平滑滚动，并能通过代码控制滚动条。
  - 消息渲染:
    - 使用 v-for 指令遍历聊天记录数组。
    - 每条消息都包含一个 头像 和一个 消息气泡。
    - 通过判断消息的 role（'user' 或 'assistant'）来决定其靠左（机器人）还是靠右（用户）显示，并应用不同的背景色。
    - 用户消息直接显示文本。
    - 机器人消息的内容会通过 v-html 指令绑定到一个经过 Markdown 解析和代码高亮处理后的 HTML 字符串上。
- 底部输入区:
  - 输入框: 使用 el-input 组件，类型为 textarea，支持自动高度调整。
  - 发送按钮: 一个带有发送图标的 el-button。在等待模型响应时，按钮应处于禁用状态，防止用户重复提交。
4. 核心功能实现说明
4.1. 状态管理
使用 Vue 3 的 Composition API，通过 ref 来定义所有响应式状态：
- chatHistory: 一个数组，存储所有对话消息对象 { role: string, content: string }。
- isLoading: 布尔值，用于控制发送按钮的禁用状态和显示加载提示。
- messageInput: 字符串，双向绑定到底部输入框的内容。
4.2. API 通信 (流式处理)
这是前端的核心逻辑。
1. 当用户发送消息时，构造一个符合 OpenAI 格式的请求体，其中必须包含 stream: true。
2. 使用 fetch API 发起 POST 请求。
3. 通过 response.body.getReader() 获取流式读取器。
4. 在一个 while 循环中不断读取数据块 (chunk)。
5. 使用 TextDecoder 将数据块解码为字符串。服务器发送的是 Server-Sent Events (SSE) 格式，每条消息以 data: 开头。
6. 解析每一行数据，提取 data: 后面的 JSON 对象。
7. 从 JSON 对象中获取增量内容 (choices[0].delta.content)，并将其追加到当前机器人回复消息的 content 属性中。
8. 当接收到 data: [DONE] 时，表示流结束，退出循环。
4.3. Markdown 与代码高亮
1. 实时渲染: 在接收流式响应的过程中，将不断增长的机器人回复内容通过 marked.parse() 转换为 HTML，并实时更新到界面上。为了提升体验，可以在内容末尾追加一个闪烁的光标。
2. 最终渲染与高亮: 当流结束后，进行一次最终的、完整的 Markdown 渲染。
3. 代码块处理:
  - marked 在转换时，会为代码块生成 <pre><code>...</code></pre> 结构。
  - 配置 highlight.js 作为 marked 的高亮器。
  - 流结束后，遍历所有新生成的 <pre> 元素，手动为其添加一个“复制”按钮。为按钮绑定点击事件，通过 navigator.clipboard.writeText() 实现复制功能。