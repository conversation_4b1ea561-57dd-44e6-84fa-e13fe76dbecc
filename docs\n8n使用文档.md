# n8n 使用文档

## 概述

n8n 是一个强大的工作流自动化工具，通过可视化的节点连接方式，让用户能够轻松创建复杂的自动化流程。本文档将详细介绍 n8n 中的各种节点类型及其功能。

## 什么是节点（Nodes）

节点是 n8n 工作流的基本构建块。它们可以是：
- **数据获取的入口点** - 从外部服务获取数据
- **数据处理功能** - 对数据进行过滤、重组和转换
- **数据输出的出口** - 将数据发送到外部服务

## 节点分类

### 1. 核心节点（Core Nodes）

核心节点是 n8n 内置的基础功能节点，提供工作流的核心操作能力。

#### 1.1 触发器节点（Trigger Nodes）
触发器节点用于启动工作流的执行：

- **Manual Trigger（手动触发器）** - 手动启动工作流
- **Schedule Trigger（定时触发器）** - 按时间计划自动执行工作流
- **Webhook（网络钩子）** - 通过HTTP请求触发工作流
- **Email Trigger (IMAP)（邮件触发器）** - 收到邮件时触发工作流
- **RSS Feed Trigger（RSS订阅触发器）** - RSS源更新时触发
- **Local File Trigger（本地文件触发器）** - 文件系统变化时触发
- **Error Trigger（错误触发器）** - 其他工作流出错时触发
- **Activation Trigger（激活触发器）** - 工作流激活时触发
- **n8n Trigger（n8n触发器）** - n8n内部事件触发
- **Workflow Trigger（工作流触发器）** - 其他工作流完成时触发
- **SSE Trigger（服务器发送事件触发器）** - 接收服务器推送事件
- **Chat Trigger（聊天触发器）** - 聊天消息触发
- **n8n Form Trigger（表单触发器）** - 表单提交时触发
- **Execute Sub-workflow Trigger（子工作流执行触发器）** - 子工作流执行时触发
- **Evaluation Trigger（评估触发器）** - 用于AI评估场景
- **MCP Server Trigger（MCP服务器触发器）** - MCP协议触发

#### 1.2 数据处理节点（Data Processing Nodes）
用于处理和转换数据：

- **Code（代码节点）** - 使用JavaScript/Python处理数据
- **Filter（过滤器）** - 根据条件过滤数据
- **If（条件判断）** - 基于条件分支执行
- **Switch（开关）** - 多条件分支路由
- **Merge（合并）** - 合并多个数据流
- **Sort（排序）** - 对数据进行排序
- **Limit（限制）** - 限制数据项数量
- **Aggregate（聚合）** - 数据聚合操作
- **Compare Datasets（数据集比较）** - 比较两个数据集
- **Remove Duplicates（去重）** - 移除重复数据
- **Split Out（拆分）** - 将数组拆分为单独项目
- **Loop Over Items (Split in Batches)（批量循环）** - 批量处理数据
- **Edit Fields (Set)（字段编辑）** - 设置或修改字段值
- **Rename Keys（重命名键）** - 重命名对象键名
- **Date & Time（日期时间）** - 日期时间处理
- **Crypto（加密）** - 加密解密操作
- **JWT（JSON Web Token）** - JWT令牌处理
- **TOTP（时间一次性密码）** - 生成TOTP验证码

#### 1.3 文件处理节点（File Processing Nodes）
处理文件和二进制数据：

- **Read/Write Files from Disk（磁盘文件读写）** - 读写本地文件
- **Extract From File（文件提取）** - 从文件中提取数据
- **Convert to File（转换为文件）** - 将数据转换为文件
- **Compression（压缩）** - 文件压缩解压
- **Edit Image（图像编辑）** - 基本图像处理
- **HTML（HTML处理）** - HTML内容处理
- **XML（XML处理）** - XML数据处理
- **Markdown（Markdown处理）** - Markdown文档处理

#### 1.4 网络通信节点（Network Communication Nodes）
处理网络请求和通信：

- **HTTP Request（HTTP请求）** - 发送HTTP请求
- **GraphQL（GraphQL查询）** - 执行GraphQL查询
- **FTP（文件传输协议）** - FTP文件操作
- **SSH（安全外壳协议）** - SSH远程执行
- **LDAP（轻量级目录访问协议）** - LDAP目录查询
- **Send Email（发送邮件）** - 发送电子邮件
- **Respond to Webhook（响应Webhook）** - 响应Webhook请求
- **Respond to Chat（响应聊天）** - 响应聊天消息

#### 1.5 工作流控制节点（Workflow Control Nodes）
控制工作流执行：

- **Wait（等待）** - 暂停工作流执行
- **Stop And Error（停止并报错）** - 停止工作流并抛出错误
- **Execute Sub-workflow（执行子工作流）** - 调用其他工作流
- **Execute Command（执行命令）** - 执行系统命令
- **No Operation, do nothing（空操作）** - 不执行任何操作
- **Debug Helper（调试助手）** - 调试工作流
- **Execution Data（执行数据）** - 获取执行信息

#### 1.6 AI和机器学习节点（AI & ML Nodes）
AI相关功能节点：

- **AI Transform（AI转换）** - 使用AI转换数据
- **Summarize（摘要）** - 文本摘要生成
- **Evaluation（评估）** - AI模型评估

#### 1.7 其他实用节点（Utility Nodes）
其他实用功能：

- **n8n（n8n管理）** - 管理n8n实例
- **n8n Form（n8n表单）** - 创建表单
- **RSS Read（RSS阅读）** - 读取RSS源
- **Git（版本控制）** - Git操作

### 2. 应用节点（App Nodes）

应用节点提供与第三方服务和应用的集成能力。

#### 2.1 云服务平台（Cloud Platforms）

**Amazon Web Services (AWS)**
- AWS Certificate Manager - SSL证书管理
- AWS Cognito - 用户身份验证
- AWS Comprehend - 自然语言处理
- AWS DynamoDB - NoSQL数据库
- AWS Elastic Load Balancing - 负载均衡
- AWS Lambda - 无服务器计算
- AWS Rekognition - 图像识别
- AWS S3 - 对象存储
- AWS SES - 邮件服务
- AWS SNS - 消息通知
- AWS SQS - 消息队列
- AWS Textract - 文档文本提取
- AWS Transcribe - 语音转文字

**Microsoft Azure**
- Azure Cosmos DB - 全球分布式数据库
- Azure Storage - 云存储服务

**Google Cloud Platform**
- Google Cloud Firestore - 文档数据库
- Google Cloud Natural Language - 自然语言API
- Google Cloud Realtime Database - 实时数据库
- Google Cloud Storage - 云存储

#### 2.2 办公协作（Office & Collaboration）

**Google Workspace**
- Google Calendar - 日历管理
- Google Chat - 即时通讯
- Google Contacts - 联系人管理
- Google Docs - 文档处理
- Google Drive - 云端硬盘
- Google Sheets - 电子表格
- Google Slides - 演示文稿
- Google Tasks - 任务管理
- Google Translate - 翻译服务
- Google Workspace Admin - 管理控制台

**Microsoft 365**
- Microsoft Dynamics CRM - 客户关系管理
- Microsoft Entra ID - 身份管理
- Microsoft Excel 365 - 电子表格
- Microsoft Graph Security - 安全图谱
- Microsoft OneDrive - 云存储
- Microsoft Outlook - 邮件客户端
- Microsoft SharePoint - 协作平台
- Microsoft Teams - 团队协作
- Microsoft To Do - 任务管理

#### 2.3 项目管理（Project Management）
- Asana - 项目管理平台
- ClickUp - 一体化工作空间
- Jira Software - 问题跟踪
- Linear - 现代项目管理
- monday.com - 工作操作系统
- Notion - 笔记和协作
- Trello - 看板式项目管理
- Todoist - 任务管理

#### 2.4 客户关系管理（CRM）
- HubSpot - 入站营销CRM
- Salesforce - 企业级CRM
- Pipedrive - 销售CRM
- Freshworks CRM - 客户关系管理
- Copper - Google集成CRM
- Agile CRM - 敏捷CRM

#### 2.5 营销自动化（Marketing Automation）
- Mailchimp - 邮件营销
- ActiveCampaign - 营销自动化
- ConvertKit - 创作者营销
- GetResponse - 邮件营销
- Brevo - 数字营销
- Customer.io - 行为驱动消息
- Iterable - 增长营销

#### 2.6 电商平台（E-commerce）
- Shopify - 电商平台
- WooCommerce - WordPress电商
- Magento 2 - 开源电商
- Stripe - 支付处理

#### 2.7 社交媒体（Social Media）
- Facebook Graph API - Facebook数据
- X (formerly Twitter) - 推特平台
- LinkedIn - 职业社交
- Discord - 社区聊天
- Telegram - 即时通讯
- WhatsApp Business Cloud - 商业通讯

#### 2.8 开发工具（Development Tools）
- GitHub - 代码托管
- GitLab - DevOps平台
- Jenkins - 持续集成
- CircleCI - 持续集成
- Docker - 容器化

#### 2.9 数据库（Databases）
- MySQL - 关系型数据库
- PostgreSQL - 开源关系型数据库
- MongoDB - 文档数据库
- Redis - 内存数据库
- Elasticsearch - 搜索引擎
- InfluxDB - 时序数据库

#### 2.10 人工智能（AI Services）
- OpenAI - GPT模型
- Anthropic - Claude模型
- Google Gemini - Google AI
- Hugging Face - AI模型平台
- Mistral AI - 开源AI模型

### 3. 社区节点（Community Nodes）

社区节点是由n8n社区开发者贡献的第三方节点，扩展了n8n的功能范围。

#### 3.1 安装社区节点
- 通过n8n界面安装验证的社区节点
- 手动安装自定义节点
- 管理节点版本和更新

#### 3.2 社区节点特点
- 丰富的第三方服务集成
- 社区维护和支持
- 可能存在兼容性风险
- 需要谨慎评估安全性

## 使用建议

### 1. 选择合适的节点
- 根据具体需求选择最适合的节点类型
- 优先使用内置节点，确保稳定性
- 谨慎使用社区节点，注意安全风险

### 2. 工作流设计原则
- 保持工作流简洁明了
- 合理使用错误处理机制
- 添加适当的调试和日志节点
- 定期测试和维护工作流

### 3. 性能优化
- 避免不必要的数据传输
- 合理使用批处理节点
- 优化数据结构和格式
- 监控工作流执行性能

### 4. 安全考虑
- 妥善管理API密钥和凭据
- 使用最小权限原则
- 定期更新节点和依赖
- 审查第三方节点的安全性

## 总结

n8n 提供了丰富的节点生态系统，涵盖了从基础数据处理到复杂的第三方服务集成的各种需求。通过合理组合这些节点，用户可以构建强大的自动化工作流，提高工作效率和业务流程的自动化水平。

选择合适的节点类型，遵循最佳实践，可以帮助您充分发挥n8n的潜力，创建稳定、高效的自动化解决方案。
