# n8n 使用文档

## 概述

n8n 是一个强大的工作流自动化工具，通过可视化的节点连接方式，让用户能够轻松创建复杂的自动化流程。本文档将详细介绍 n8n 中的各种节点类型及其功能。

## 什么是节点（Nodes）

节点是 n8n 工作流的基本构建块。它们可以是：
- **数据获取的入口点** - 从外部服务获取数据
- **数据处理功能** - 对数据进行过滤、重组和转换
- **数据输出的出口** - 将数据发送到外部服务

## 节点分类

### 1. 核心节点（Core Nodes）

核心节点是 n8n 内置的基础功能节点，提供工作流的核心操作能力。

#### 1.1 触发器节点（Trigger Nodes）
触发器节点用于启动工作流的执行：

- **Manual Trigger（手动触发器）** - 手动启动工作流
- **Schedule Trigger（定时触发器）** - 按时间计划自动执行工作流
- **Webhook（网络钩子）** - 通过HTTP请求触发工作流
- **Email Trigger (IMAP)（邮件触发器）** - 收到邮件时触发工作流
- **RSS Feed Trigger（RSS订阅触发器）** - RSS源更新时触发
- **Local File Trigger（本地文件触发器）** - 文件系统变化时触发
- **Error Trigger（错误触发器）** - 其他工作流出错时触发
- **Activation Trigger（激活触发器）** - 工作流激活时触发
- **n8n Trigger（n8n触发器）** - n8n内部事件触发
- **Workflow Trigger（工作流触发器）** - 其他工作流完成时触发
- **SSE Trigger（服务器发送事件触发器）** - 接收服务器推送事件
- **Chat Trigger（聊天触发器）** - 聊天消息触发
- **n8n Form Trigger（表单触发器）** - 表单提交时触发
- **Execute Sub-workflow Trigger（子工作流执行触发器）** - 子工作流执行时触发
- **Evaluation Trigger（评估触发器）** - 用于AI评估场景
- **MCP Server Trigger（MCP服务器触发器）** - MCP协议触发

#### 1.2 数据处理节点（Data Processing Nodes）
用于处理和转换数据：

- **Code（代码节点）** - 使用JavaScript/Python处理数据
- **Filter（过滤器）** - 根据条件过滤数据
- **If（条件判断）** - 基于条件分支执行
- **Switch（开关）** - 多条件分支路由
- **Merge（合并）** - 合并多个数据流
- **Sort（排序）** - 对数据进行排序
- **Limit（限制）** - 限制数据项数量
- **Aggregate（聚合）** - 数据聚合操作
- **Compare Datasets（数据集比较）** - 比较两个数据集
- **Remove Duplicates（去重）** - 移除重复数据
- **Split Out（拆分）** - 将数组拆分为单独项目
- **Loop Over Items (Split in Batches)（批量循环）** - 批量处理数据
- **Edit Fields (Set)（字段编辑）** - 设置或修改字段值
- **Rename Keys（重命名键）** - 重命名对象键名
- **Date & Time（日期时间）** - 日期时间处理
- **Crypto（加密）** - 加密解密操作
- **JWT（JSON Web Token）** - JWT令牌处理
- **TOTP（时间一次性密码）** - 生成TOTP验证码

#### 1.3 文件处理节点（File Processing Nodes）
处理文件和二进制数据：

- **Read/Write Files from Disk（磁盘文件读写）** - 读写本地文件
- **Extract From File（文件提取）** - 从文件中提取数据
- **Convert to File（转换为文件）** - 将数据转换为文件
- **Compression（压缩）** - 文件压缩解压
- **Edit Image（图像编辑）** - 基本图像处理
- **HTML（HTML处理）** - HTML内容处理
- **XML（XML处理）** - XML数据处理
- **Markdown（Markdown处理）** - Markdown文档处理

#### 1.4 网络通信节点（Network Communication Nodes）
处理网络请求和通信：

- **HTTP Request（HTTP请求）** - 发送HTTP请求
- **GraphQL（GraphQL查询）** - 执行GraphQL查询
- **FTP（文件传输协议）** - FTP文件操作
- **SSH（安全外壳协议）** - SSH远程执行
- **LDAP（轻量级目录访问协议）** - LDAP目录查询
- **Send Email（发送邮件）** - 发送电子邮件
- **Respond to Webhook（响应Webhook）** - 响应Webhook请求
- **Respond to Chat（响应聊天）** - 响应聊天消息

#### 1.5 工作流控制节点（Workflow Control Nodes）
控制工作流执行：

- **Wait（等待）** - 暂停工作流执行
- **Stop And Error（停止并报错）** - 停止工作流并抛出错误
- **Execute Sub-workflow（执行子工作流）** - 调用其他工作流
- **Execute Command（执行命令）** - 执行系统命令
- **No Operation, do nothing（空操作）** - 不执行任何操作
- **Debug Helper（调试助手）** - 调试工作流
- **Execution Data（执行数据）** - 获取执行信息

#### 1.6 AI和机器学习节点（AI & ML Nodes）
AI相关功能节点：

- **AI Transform（AI转换）** - 使用AI转换数据
- **Summarize（摘要）** - 文本摘要生成
- **Evaluation（评估）** - AI模型评估

#### 1.7 其他实用节点（Utility Nodes）
其他实用功能：

- **n8n（n8n管理）** - 管理n8n实例
- **n8n Form（n8n表单）** - 创建表单
- **RSS Read（RSS阅读）** - 读取RSS源
- **Git（版本控制）** - Git操作

### 2. 应用节点（App Nodes）

应用节点提供与第三方服务和应用的集成能力。

#### 2.1 云服务平台（Cloud Platforms）

**Amazon Web Services (AWS)**
- AWS Certificate Manager - SSL证书管理
- AWS Cognito - 用户身份验证
- AWS Comprehend - 自然语言处理
- AWS DynamoDB - NoSQL数据库
- AWS Elastic Load Balancing - 负载均衡
- AWS Lambda - 无服务器计算
- AWS Rekognition - 图像识别
- AWS S3 - 对象存储
- AWS SES - 邮件服务
- AWS SNS - 消息通知
- AWS SQS - 消息队列
- AWS Textract - 文档文本提取
- AWS Transcribe - 语音转文字

**Microsoft Azure**
- Azure Cosmos DB - 全球分布式数据库
- Azure Storage - 云存储服务

**Google Cloud Platform**
- Google Cloud Firestore - 文档数据库
- Google Cloud Natural Language - 自然语言API
- Google Cloud Realtime Database - 实时数据库
- Google Cloud Storage - 云存储

#### 2.2 办公协作（Office & Collaboration）

**Google Workspace**
- Google Calendar - 日历管理
- Google Chat - 即时通讯
- Google Contacts - 联系人管理
- Google Docs - 文档处理
- Google Drive - 云端硬盘
- Google Sheets - 电子表格
- Google Slides - 演示文稿
- Google Tasks - 任务管理
- Google Translate - 翻译服务
- Google Workspace Admin - 管理控制台

**Microsoft 365**
- Microsoft Dynamics CRM - 客户关系管理
- Microsoft Entra ID - 身份管理
- Microsoft Excel 365 - 电子表格
- Microsoft Graph Security - 安全图谱
- Microsoft OneDrive - 云存储
- Microsoft Outlook - 邮件客户端
- Microsoft SharePoint - 协作平台
- Microsoft Teams - 团队协作
- Microsoft To Do - 任务管理

#### 2.3 项目管理（Project Management）
- Asana - 项目管理平台
- ClickUp - 一体化工作空间
- Jira Software - 问题跟踪
- Linear - 现代项目管理
- monday.com - 工作操作系统
- Notion - 笔记和协作
- Trello - 看板式项目管理
- Todoist - 任务管理

#### 2.4 客户关系管理（CRM）
- HubSpot - 入站营销CRM
- Salesforce - 企业级CRM
- Pipedrive - 销售CRM
- Freshworks CRM - 客户关系管理
- Copper - Google集成CRM
- Agile CRM - 敏捷CRM

#### 2.5 营销自动化（Marketing Automation）
- Mailchimp - 邮件营销
- ActiveCampaign - 营销自动化
- ConvertKit - 创作者营销
- GetResponse - 邮件营销
- Brevo - 数字营销
- Customer.io - 行为驱动消息
- Iterable - 增长营销

#### 2.6 电商平台（E-commerce）
- Shopify - 电商平台
- WooCommerce - WordPress电商
- Magento 2 - 开源电商
- Stripe - 支付处理

#### 2.7 社交媒体（Social Media）
- Facebook Graph API - Facebook数据
- X (formerly Twitter) - 推特平台
- LinkedIn - 职业社交
- Discord - 社区聊天
- Telegram - 即时通讯
- WhatsApp Business Cloud - 商业通讯

#### 2.8 开发工具（Development Tools）
- GitHub - 代码托管
- GitLab - DevOps平台
- Jenkins - 持续集成
- CircleCI - 持续集成
- Docker - 容器化

#### 2.9 数据库（Databases）
- MySQL - 关系型数据库
- PostgreSQL - 开源关系型数据库
- MongoDB - 文档数据库
- Redis - 内存数据库
- Elasticsearch - 搜索引擎
- InfluxDB - 时序数据库

#### 2.10 人工智能（AI Services）
- **OpenAI** - GPT模型和AI服务
- **Anthropic** - Claude AI助手
- **Google Gemini** - Google的AI模型
- **Hugging Face** - AI模型和数据集平台
- **Mistral AI** - 开源大语言模型
- **Ollama** - 本地运行大语言模型
- **Perplexity** - AI搜索引擎
- **xAI** - Elon Musk的AI公司
- **OpenRouter** - AI模型路由服务

#### 2.11 通讯和消息（Communication & Messaging）
- **Twilio** - 云通讯API平台
- **Vonage** - 通讯API服务
- **MessageBird** - 全渠道通讯平台
- **Plivo** - 云通讯平台
- **MSG91** - 全球通讯API
- **Mocean** - 通讯API服务
- **seven** - SMS和语音API
- **Gotify** - 自托管通知服务
- **Pushbullet** - 跨设备推送通知
- **Pushover** - 实时通知服务
- **Pushcut** - iOS自动化通知
- **SIGNL4** - 移动警报和通知

#### 2.12 文件存储和管理（File Storage & Management）
- **Dropbox** - 云文件存储和同步
- **Box** - 企业内容管理平台
- **Nextcloud** - 自托管文件同步和共享
- **Webflow** - 可视化网页设计平台
- **Contentful** - 无头内容管理系统
- **Strapi** - 开源无头CMS
- **Ghost** - 现代发布平台
- **WordPress** - 内容管理系统
- **Storyblok** - 无头CMS平台

#### 2.13 监控和分析（Monitoring & Analytics）
- **Grafana** - 可视化和监控平台
- **PostHog** - 产品分析平台
- **Metabase** - 开源商业智能工具
- **Sentry.io** - 错误监控和性能监控
- **UptimeRobot** - 网站监控服务
- **PagerDuty** - 数字运维管理平台

#### 2.14 安全工具（Security Tools）
- **VirusTotal** - 恶意软件检测服务
- **urlscan.io** - URL和网站扫描
- **MISP** - 恶意软件信息共享平台
- **TheHive** - 安全事件响应平台
- **Cortex** - 可观察对象分析引擎
- **OpenCTI** - 网络威胁情报平台
- **Splunk** - 数据分析和安全平台
- **Elastic Security** - 安全分析平台

#### 2.15 金融和会计（Finance & Accounting）
- **QuickBooks** - 小企业会计软件
- **Xero** - 云端会计软件
- **Invoice Ninja** - 发票和计费管理
- **Stripe** - 在线支付处理
- **PayPal** - 数字支付解决方案
- **Wise** - 国际汇款服务
- **CoinGecko** - 加密货币市场数据

#### 2.16 人力资源（Human Resources）
- **BambooHR** - 人力资源管理系统
- **Workable** - 招聘管理平台
- **Harvest** - 时间跟踪和发票管理
- **Toggl** - 时间跟踪工具
- **Clockify** - 免费时间跟踪软件

#### 2.17 客户支持（Customer Support）
- **Freshdesk** - 客户支持软件
- **Freshservice** - IT服务管理
- **Help Scout** - 客户服务平台
- **Intercom** - 客户消息平台
- **Zendesk** - 客户服务平台
- **ServiceNow** - 企业服务管理

#### 2.18 调研和表单（Survey & Forms）
- **Typeform** - 在线表单和调研工具
- **SurveyMonkey** - 在线调研平台
- **JotForm** - 在线表单构建器
- **Wufoo** - 表单构建和数据收集
- **KoboToolbox** - 人道主义数据收集

### 3. 社区节点（Community Nodes）

社区节点是由n8n社区开发者贡献的第三方节点，极大扩展了n8n的功能范围。

#### 3.1 社区节点特点
- **丰富的集成** - 覆盖更多第三方服务和专业工具
- **社区维护** - 由开发者社区维护和更新
- **快速迭代** - 新功能和修复更新较快
- **多样化** - 涵盖各种行业和用例的专业需求

#### 3.2 安装社区节点

**通过n8n界面安装（推荐）**
1. 在n8n界面中点击"设置" → "社区节点"
2. 搜索需要的节点包
3. 点击"安装"按钮
4. 等待安装完成并重启n8n

**手动安装**
```bash
# 使用npm安装
npm install n8n-nodes-[package-name]

# 或使用yarn安装
yarn add n8n-nodes-[package-name]
```

#### 3.3 验证的社区节点
n8n官方验证的社区节点具有以下特点：
- 经过安全审查
- 代码质量较高
- 文档完善
- 定期维护更新

#### 3.4 使用注意事项
- **安全风险** - 第三方代码可能存在安全隐患
- **兼容性** - 可能与n8n版本不兼容
- **维护状态** - 注意节点的维护状态和更新频率
- **依赖管理** - 可能引入额外的依赖包

## 节点选择指南

### 1. 根据功能需求选择

**数据处理场景**
- 简单条件判断：使用 `If` 节点
- 复杂逻辑处理：使用 `Code` 节点
- 数据过滤：使用 `Filter` 节点
- 数据合并：使用 `Merge` 节点
- 批量处理：使用 `Loop Over Items` 节点

**外部服务集成**
- 优先选择官方内置节点
- 查看节点的功能覆盖度
- 考虑API限制和配额
- 评估数据安全要求

**工作流触发**
- 定时任务：使用 `Schedule Trigger`
- 外部事件：使用 `Webhook`
- 文件变化：使用 `Local File Trigger`
- 邮件接收：使用 `Email Trigger`

### 2. 性能考虑

**数据量处理**
- 大数据量：使用批处理节点
- 实时处理：选择高性能节点
- 内存使用：注意节点的内存消耗
- 执行时间：避免长时间运行的操作

**网络请求**
- 并发限制：注意API的并发限制
- 重试机制：配置适当的重试策略
- 超时设置：设置合理的超时时间
- 缓存策略：合理使用缓存减少请求

### 3. 安全最佳实践

**凭据管理**
- 使用n8n的凭据管理系统
- 定期轮换API密钥
- 最小权限原则
- 避免在工作流中硬编码敏感信息

**数据保护**
- 加密敏感数据
- 限制数据访问范围
- 定期清理临时数据
- 遵守数据保护法规

**网络安全**
- 使用HTTPS连接
- 验证SSL证书
- 限制网络访问
- 监控异常活动

## 常见使用场景

### 1. 数据同步
- **CRM数据同步** - 在不同CRM系统间同步客户数据
- **数据库同步** - 在多个数据库间保持数据一致性
- **文件同步** - 在云存储服务间同步文件

### 2. 营销自动化
- **邮件营销** - 自动发送个性化邮件
- **社交媒体管理** - 自动发布和监控社交媒体内容
- **潜在客户培育** - 基于行为触发营销活动

### 3. 业务流程自动化
- **订单处理** - 自动化订单确认和发货流程
- **发票管理** - 自动生成和发送发票
- **报告生成** - 定期生成和分发业务报告

### 4. 监控和警报
- **系统监控** - 监控服务器和应用状态
- **业务指标监控** - 跟踪关键业务指标
- **异常警报** - 在出现问题时自动发送警报

### 5. 内容管理
- **内容发布** - 自动发布博客文章和新闻
- **媒体处理** - 自动处理和优化图片视频
- **备份管理** - 定期备份重要内容

## 故障排除

### 1. 常见问题

**节点执行失败**
- 检查节点配置是否正确
- 验证凭据是否有效
- 查看错误日志获取详细信息
- 检查网络连接状态

**数据格式问题**
- 使用 `Debug Helper` 节点查看数据结构
- 检查数据类型是否匹配
- 使用 `Code` 节点进行数据转换
- 验证JSON格式是否正确

**性能问题**
- 检查工作流的执行时间
- 优化数据处理逻辑
- 减少不必要的API调用
- 使用批处理提高效率

### 2. 调试技巧

**使用调试节点**
- 添加 `Debug Helper` 节点查看数据
- 使用 `Stop And Error` 节点暂停执行
- 启用详细日志记录
- 分步测试工作流

**监控执行**
- 查看执行历史记录
- 分析执行时间和资源使用
- 设置执行超时限制
- 监控错误率和成功率

## 最佳实践

### 1. 工作流设计
- **模块化设计** - 将复杂工作流拆分为小的模块
- **错误处理** - 为每个关键步骤添加错误处理
- **文档说明** - 为工作流添加清晰的说明和注释
- **版本控制** - 定期备份和版本化工作流

### 2. 性能优化
- **批量处理** - 尽可能使用批量操作
- **缓存利用** - 合理使用缓存减少重复计算
- **并行执行** - 利用并行分支提高效率
- **资源管理** - 监控和限制资源使用

### 3. 安全管理
- **权限控制** - 实施最小权限原则
- **数据加密** - 加密传输和存储的敏感数据
- **审计日志** - 记录所有重要操作
- **定期审查** - 定期审查和更新安全配置

### 4. 维护管理
- **定期测试** - 定期测试工作流的正确性
- **监控告警** - 设置监控和告警机制
- **文档更新** - 保持文档的及时更新
- **培训支持** - 为团队提供必要的培训

## 总结

n8n 提供了一个强大而灵活的节点生态系统，从基础的数据处理到复杂的第三方服务集成，几乎涵盖了所有自动化需求。通过合理选择和组合这些节点，用户可以构建出高效、稳定的自动化工作流。

**关键要点：**

### 1. 节点分类清晰
- **核心节点** - 提供基础功能，稳定可靠
- **应用节点** - 丰富的第三方集成，覆盖各行各业
- **社区节点** - 扩展功能，需谨慎使用

### 2. 功能覆盖全面
- **触发器节点** - 多种触发方式，满足不同场景
- **数据处理** - 从简单过滤到复杂转换
- **外部集成** - 支持数百种服务和应用
- **工作流控制** - 灵活的流程控制机制

### 3. 选择策略重要
- **功能匹配** - 根据需求选择最合适的节点
- **性能考虑** - 评估节点对系统性能的影响
- **安全评估** - 特别关注第三方节点的安全性
- **维护成本** - 考虑长期维护和更新成本

### 4. 最佳实践必要
- **设计原则** - 模块化、可维护、可扩展
- **安全管理** - 凭据保护、权限控制、数据加密
- **性能优化** - 批量处理、缓存利用、资源管理
- **故障处理** - 错误捕获、重试机制、监控告警

### 5. 持续学习和改进
- **跟上更新** - n8n和节点生态系统在不断发展
- **社区参与** - 积极参与社区讨论和经验分享
- **实践积累** - 通过实际项目积累经验和最佳实践
- **创新应用** - 探索新的自动化场景和解决方案

通过深入理解各类节点的特点和适用场景，结合实际业务需求，您可以充分发挥n8n的潜力，创建出真正有价值的自动化解决方案。无论是简单的数据同步，还是复杂的业务流程自动化，n8n都能提供相应的工具和支持。

记住，成功的自动化不仅仅是技术的实现，更需要对业务流程的深入理解和持续的优化改进。希望这份文档能够帮助您更好地使用n8n，提升工作效率和业务价值。

---

**相关资源：**
- [n8n官方文档](https://docs.n8n.io/)
- [n8n社区论坛](https://community.n8n.io/)
- [n8n工作流模板](https://n8n.io/workflows/)
- [n8n GitHub仓库](https://github.com/n8n-io/n8n)
