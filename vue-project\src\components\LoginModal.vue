<template>
  <el-dialog
    v-model="visible"
    title="手机号登录"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="login-form">
      <!-- 步骤1: 输入手机号 -->
      <div v-if="step === 1">
        <el-form :model="loginForm" :rules="phoneRules" ref="phoneFormRef">
          <el-form-item label="手机号" prop="phoneNumber">
            <el-input
              v-model="loginForm.phoneNumber"
              placeholder="请输入手机号"
              maxlength="11"
              @keyup.enter="sendVerificationCode"
            />
          </el-form-item>
        </el-form>
        
        <div class="form-actions">
          <el-button 
            type="primary" 
            @click="sendVerificationCode"
            :loading="sendingCode"
            style="width: 100%"
          >
            {{ sendingCode ? '发送中...' : '发送验证码' }}
          </el-button>
        </div>
        
        <div class="login-tips">
          <el-alert
            title="仅限内部邀请用户使用"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
      
      <!-- 步骤2: 输入验证码 -->
      <div v-if="step === 2">
        <el-form :model="loginForm" :rules="codeRules" ref="codeFormRef">
          <el-form-item label="手机号">
            <el-input v-model="loginForm.phoneNumber" disabled />
          </el-form-item>
          
          <el-form-item label="验证码" prop="verificationCode">
            <el-input
              v-model="loginForm.verificationCode"
              placeholder="请输入6位验证码"
              maxlength="6"
              @keyup.enter="verifyLogin"
            />
          </el-form-item>
        </el-form>
        
        <div class="countdown-info" v-if="countdown > 0">
          <span>验证码已发送，{{ countdown }}秒后可重新发送</span>
        </div>
        
        <div class="form-actions">
          <el-button @click="goBack" style="width: 48%">
            返回
          </el-button>
          <el-button 
            type="primary" 
            @click="verifyLogin"
            :loading="verifying"
            style="width: 48%"
          >
            {{ verifying ? '验证中...' : '登录' }}
          </el-button>
        </div>
        
        <div class="resend-section" v-if="countdown === 0">
          <el-button 
            type="text" 
            @click="sendVerificationCode"
            :loading="sendingCode"
          >
            重新发送验证码
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import { sendVerificationCodeApi, verifyLoginApi } from '../utils/authApi.js';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'login-success']);

// 响应式数据
const visible = ref(props.modelValue);
const step = ref(1); // 1: 输入手机号, 2: 输入验证码
const sendingCode = ref(false);
const verifying = ref(false);
const countdown = ref(0);
const phoneFormRef = ref(null);
const codeFormRef = ref(null);

// 表单数据
const loginForm = reactive({
  phoneNumber: '',
  verificationCode: ''
});

// 表单验证规则
const phoneRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
};

const codeRules = {
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
  ]
};

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
  if (!newVal) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  step.value = 1;
  loginForm.phoneNumber = '';
  loginForm.verificationCode = '';
  countdown.value = 0;
  sendingCode.value = false;
  verifying.value = false;
};

// 发送验证码
const sendVerificationCode = async () => {
  if (!phoneFormRef.value) return;
  
  try {
    await phoneFormRef.value.validate();
    sendingCode.value = true;
    
    const response = await sendVerificationCodeApi(loginForm.phoneNumber);
    
    ElMessage.success('验证码发送成功');
    step.value = 2;
    startCountdown();
    
    // 开发环境显示验证码
    if (response.code) {
      ElNotification({
        title: '开发环境提示',
        message: `验证码: ${response.code}`,
        type: 'info',
        duration: 10000
      });
    }
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error(error.message || '发送验证码失败');
  } finally {
    sendingCode.value = false;
  }
};

// 验证登录
const verifyLogin = async () => {
  if (!codeFormRef.value) return;
  
  try {
    await codeFormRef.value.validate();
    verifying.value = true;
    
    const response = await verifyLoginApi(
      loginForm.phoneNumber, 
      loginForm.verificationCode
    );
    
    ElMessage.success('登录成功');
    
    // 存储登录信息
    localStorage.setItem('userInfo', JSON.stringify({
      phoneNumber: response.phone_number,
      sessionToken: response.session_token,
      apiKey: response.api_key,
      loginTime: Date.now()
    }));
    
    // 触发登录成功事件
    emit('login-success', response);
    
    // 关闭对话框
    visible.value = false;
    
  } catch (error) {
    console.error('登录验证失败:', error);
    ElMessage.error(error.message || '登录验证失败');
  } finally {
    verifying.value = false;
  }
};

// 返回上一步
const goBack = () => {
  step.value = 1;
  loginForm.verificationCode = '';
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};
</script>

<style scoped>
.login-form {
  padding: 20px 0;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.login-tips {
  margin-top: 15px;
}

.countdown-info {
  margin: 10px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.resend-section {
  margin-top: 15px;
  text-align: center;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-dialog__body {
  padding: 20px 20px 30px;
}
</style>
