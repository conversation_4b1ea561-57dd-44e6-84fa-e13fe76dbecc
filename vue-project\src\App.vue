<template>
  <div class="app-container">
    <!-- 登录状态检查 -->
    <div v-if="!isLoggedIn" class="login-prompt">
      <div class="login-content">
        <h1>欢迎使用 TCM GuanBao LLM</h1>
        <p>请登录后开始使用</p>
        <el-button type="primary" size="large" @click="showLoginModal = true">
          手机号登录
        </el-button>
      </div>
    </div>

    <!-- 已登录状态 - 完整聊天界面 -->
    <el-container v-else class="app-layout">
      <!-- 左侧边栏 -->
      <el-aside width="320px" class="chat-sidebar">
        <div class="sidebar-header">
          <el-button type="primary" class="new-chat-btn" @click="newChat">
            <el-icon><Plus /></el-icon>
            新建对话
          </el-button>
        </div>

        <div class="chat-history">
          <div class="history-header">
            <span>会话历史</span>
          </div>
          <div class="history-list">
            <div
              v-for="session in chatSessions"
              :key="session.id"
              class="history-item"
              :class="{ active: session.id === currentSessionId }"
            >
              <div class="session-content" @click="switchToSession(session.id)">
                <div class="session-title">{{ session.title }}</div>
                <div class="session-time">{{ formatTime(session.updatedAt) }}</div>
              </div>
              <el-button
                type="text"
                size="small"
                class="delete-btn"
                @click.stop="deleteSession(session.id)"
                title="删除会话"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <div v-if="chatSessions.length === 0" class="no-history">
              暂无会话历史
            </div>
          </div>
        </div>

        <!-- 用户信息和登出 -->
        <div class="user-section">
          <div class="user-info">
            <el-icon><User /></el-icon>
            <span class="phone-number">{{ currentUser }}</span>
          </div>
          <el-button
            type="text"
            class="logout-btn"
            @click="handleLogout"
            title="退出登录"
          >
            <el-icon><SwitchButton /></el-icon>
          </el-button>
        </div>
      </el-aside>

      <!-- 右侧主聊天区 -->
      <el-main class="chat-main">
        <!-- 调试按钮 -->
        <el-button
          class="debug-btn"
          type="info"
          size="small"
          circle
          @click="showDebugPanel = true"
          title="API调试面板"
        >
          <el-icon><Setting /></el-icon>
        </el-button>

        <!-- 欢迎界面 -->
        <div v-if="!chatHistory.length" class="welcome-screen">
          <div class="logo">GuanBao AI</div>

          <!-- 服务器状态显示（隐藏） -->
          <!-- <div class="server-status">
            <el-alert
              v-if="serverStatus === 'healthy'"
              title="GuanBao 服务已连接"
              :description="`用户: ${currentUser}`"
              type="success"
              :closable="false"
              show-icon
            />
            <el-alert
              v-else-if="serverStatus === 'error'"
              title="GuanBao 服务连接失败"
              description="请检查后端服务器是否正在运行"
              type="error"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>请检查后端服务器是否正在运行在 http://localhost:82</p>
                <el-button size="small" type="primary" @click="initializeConnection">
                  重新连接
                </el-button>
              </template>
            </el-alert>
            <el-alert
              v-else
              title="正在连接 GuanBao 服务..."
              type="info"
              :closable="false"
              show-icon
            />
          </div> -->

          <div class="suggestion-cards">
            <el-card
              v-for="(card, index) in suggestionCards"
              :key="index"
              @click="sendSuggestion(card.prompt)"
              class="suggestion-card"
              shadow="hover"
              :class="{ disabled: serverStatus !== 'healthy' }"
            >
              <h3>{{ card.title }}</h3>
              <p>{{ card.prompt }}</p>
            </el-card>
          </div>
        </div>

        <!-- 聊天记录区 -->
        <div v-else class="chat-container">
          <el-scrollbar ref="scrollbarRef" class="chat-messages">
            <div v-for="(message, index) in chatHistory" :key="index" class="message-wrapper" :class="message.role">
              <div class="avatar">{{ message.role === 'user' ? 'U' : 'AI' }}</div>
              <div class="message" v-html="message.content"></div>
            </div>
          </el-scrollbar>
        </div>

        <!-- 输入区 -->
        <div class="chat-input-area">
          <el-form @submit.prevent="handleSend(messageInput)" class="input-wrapper">
            <el-input
              v-model="messageInput"
              type="textarea"
              :rows="1"
              :autosize="{ minRows: 1, maxRows: 4 }"
              placeholder="输入您的问题..."
              class="chat-input"
              @keydown.enter.exact.prevent="handleSend(messageInput)"
              @keydown.enter.shift.exact="handleShiftEnter"
              :disabled="isLoading"
            />
            <!-- 发送按钮 -->
            <el-button
              v-if="!isGenerating"
              type="primary"
              @click="handleSend(messageInput)"
              :disabled="!messageInput.trim() || isLoading"
              :loading="isLoading"
              class="send-button"
              circle
            >
              <el-icon><Promotion /></el-icon>
            </el-button>

            <!-- 停止生成按钮 -->
            <el-button
              v-else
              type="danger"
              @click="stopGeneration"
              class="stop-button"
              circle
              title="停止生成"
            >
              <el-icon><VideoPause /></el-icon>
            </el-button>
          </el-form>
        </div>

        <!-- API调试面板 -->
        <el-drawer
          v-model="showDebugPanel"
          title="API 调试面板"
          direction="rtl"
          size="50%"
        >
          <div class="debug-content">
            <el-button @click="testConnection">测试连接</el-button>
            <p>调试功能开发中...</p>
          </div>
        </el-drawer>
      </el-main>
    </el-container>
    
    <!-- 登录对话框 -->
    <el-dialog
      v-model="showLoginModal"
      title="手机号登录"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="login-form">
        <el-form>
          <el-form-item label="手机号">
            <el-input 
              v-model="phoneNumber" 
              placeholder="请输入手机号"
              maxlength="11"
            />
          </el-form-item>
          <el-form-item label="验证码" v-if="showCodeInput">
            <el-input 
              v-model="verificationCode" 
              placeholder="请输入验证码"
              maxlength="6"
            />
          </el-form-item>
        </el-form>
        
        <div class="form-actions">
          <el-button 
            v-if="!showCodeInput"
            type="primary" 
            @click="sendCode"
            :loading="loading"
            style="width: 100%"
          >
            发送验证码
          </el-button>
          <el-button 
            v-else
            type="primary" 
            @click="verifyCode"
            :loading="loading"
            style="width: 100%"
          >
            登录
          </el-button>
        </div>
        
        <div class="tips">
          <el-alert
            title="测试手机号: 13900000000, 验证码: 123456"
            type="info"
            :closable="false"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElNotification, ElMessageBox } from 'element-plus';
import { Plus, User, SwitchButton, Setting, Promotion, Delete, VideoPause } from '@element-plus/icons-vue';
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';

// 登录相关数据
const isLoggedIn = ref(false);
const currentUser = ref('');
const showLoginModal = ref(false);
const phoneNumber = ref('');
const verificationCode = ref('');
const showCodeInput = ref(false);
const loading = ref(false);

// 聊天相关数据
const chatHistory = ref([]);
const messageInput = ref('');
const isLoading = ref(false);
const scrollbarRef = ref(null);
const serverStatus = ref('unknown'); // unknown, healthy, error
const showDebugPanel = ref(false);

// 会话管理数据
const chatSessions = ref([]);
const currentSessionId = ref(null);

// 停止生成相关
const isGenerating = ref(false);
const abortController = ref(null);

// 建议卡片数据
const suggestionCards = ref([
  { title: '解释概念', prompt: '晚上睡不着觉怎么办？' },
  { title: '写商务邮件', prompt: '患者主诉：活动后胸痛一周。现病史：患者一周前活动后出现胸口隐隐作痛，如针刺样乏力气短，活动后汗出，偏头痛。中医望闻切诊：表情自然，面色红润，形体正常,语气清,气息平；无异常气味,舌暗红，苔少。请你根据上述患者的主诉、病史和中医望闻切诊情况，判断该患者的主要中医疾病和中医证型，并给出中医辨病辨证的依据。' },
  { title: '编写代码', prompt: '写一个Python函数，实现快速排序算法' },
  { title: '提供建议', prompt: '我应该如何开始学习一门新的语言？' }
]);

// 初始化
onMounted(() => {
  console.log('App组件已挂载');

  // 配置 marked 和 highlight.js
  marked.setOptions({
    highlight: function(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, { language }).value;
    },
    langPrefix: 'hljs language-',
  });

  checkLoginStatus();
  loadSessions();
});

// 检查登录状态
const checkLoginStatus = () => {
  const userInfo = localStorage.getItem('userInfo');
  if (userInfo) {
    try {
      const user = JSON.parse(userInfo);
      isLoggedIn.value = true;
      currentUser.value = user.phoneNumber;
      // 登录后初始化连接
      initializeConnection();
    } catch (error) {
      console.error('解析用户信息失败:', error);
      localStorage.removeItem('userInfo');
    }
  }
};

// 发送验证码
const sendCode = async () => {
  if (!phoneNumber.value) {
    ElMessage.error('请输入手机号');
    return;
  }
  
  if (!/^1[3-9]\d{9}$/.test(phoneNumber.value)) {
    ElMessage.error('请输入正确的手机号');
    return;
  }
  
  // 检查是否为测试手机号
  if (phoneNumber.value !== '13900000000') {
    ElMessage.error('该手机号未获得使用邀请');
    return;
  }
  
  loading.value = true;
  
  try {
    // 模拟发送验证码
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    showCodeInput.value = true;
    ElMessage.success('验证码发送成功');
    ElMessage.info('测试验证码：123456');
    
  } catch (error) {
    ElMessage.error('发送验证码失败');
  } finally {
    loading.value = false;
  }
};

// 验证登录
const verifyCode = async () => {
  if (!verificationCode.value) {
    ElMessage.error('请输入验证码');
    return;
  }
  
  loading.value = true;
  
  try {
    // 模拟验证
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (verificationCode.value === '123456') {
      // 保存用户信息
      const userInfo = {
        phoneNumber: phoneNumber.value,
        sessionToken: 'test-token-' + Date.now(),
        apiKey: 'GuanBao_2024_API_KEY',
        loginTime: Date.now()
      };
      
      localStorage.setItem('userInfo', JSON.stringify(userInfo));
      
      // 更新状态
      isLoggedIn.value = true;
      currentUser.value = phoneNumber.value;
      
      ElMessage.success('登录成功');
      showLoginModal.value = false;
      
      // 重置表单
      phoneNumber.value = '';
      verificationCode.value = '';
      showCodeInput.value = false;
      
    } else {
      ElMessage.error('验证码错误');
    }
    
  } catch (error) {
    ElMessage.error('登录验证失败');
  } finally {
    loading.value = false;
  }
};

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('userInfo');
  isLoggedIn.value = false;
  currentUser.value = '';
  chatHistory.value = [];
  serverStatus.value = 'unknown';
  ElMessage.info('已退出登录');
};

// 初始化连接
const initializeConnection = async () => {
  try {
    console.log('正在检查GuanBao后端服务连接...');

    const userInfo = getCurrentUser();
    if (!userInfo) {
      serverStatus.value = 'error';
      return;
    }

    // 检查服务器健康状态
    const isHealthy = await checkServerHealth();

    if (isHealthy) {
      serverStatus.value = 'healthy';
      ElNotification({
        title: '连接成功',
        message: `已成功连接到GuanBao服务，用户: ${userInfo.phoneNumber}`,
        type: 'success',
        duration: 3000
      });
    } else {
      serverStatus.value = 'error';
      ElNotification({
        title: '连接失败',
        message: 'GuanBao后端服务不可用，请检查服务器是否正在运行',
        type: 'error',
        duration: 0
      });
    }

  } catch (error) {
    console.error('初始化连接失败:', error);
    serverStatus.value = 'error';
  }
};

// 获取当前用户信息
const getCurrentUser = () => {
  try {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      return JSON.parse(userInfo);
    }
    return null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

// 检查服务器健康状态
const checkServerHealth = async () => {
  try {
    const response = await fetch('/api/health', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    if (response.ok) {
      console.log('GuanBao 后端服务健康检查通过');
      return true;
    } else {
      console.warn('GuanBao 后端服务健康检查失败:', response.status);
      return false;
    }
  } catch (error) {
    console.error('GuanBao 后端服务健康检查错误:', error);
    return false;
  }
};

// 处理发送消息
const handleSend = async (message) => {
  if (!message || !message.trim()) return;

  // 检查用户登录状态
  const userInfo = getCurrentUser();
  if (!userInfo) {
    ElMessage.error('用户未登录，请先登录');
    return;
  }

  // 检查服务器状态
  if (serverStatus.value === 'error') {
    ElMessage.error('GuanBao服务不可用，请检查服务器连接');
    return;
  }

  // 确保有当前会话，发送消息时才创建
  if (!currentSessionId.value) {
    const newSessionId = Date.now().toString();
    const newSession = {
      id: newSessionId,
      title: '新对话',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    chatSessions.value.unshift(newSession);
    currentSessionId.value = newSessionId;
  }

  // 添加用户消息
  chatHistory.value.push({ role: 'user', content: message.trim() });
  messageInput.value = '';
  isLoading.value = true;
  isGenerating.value = true;

  // 创建新的AbortController
  abortController.value = new AbortController();

  await scrollToBottom();

  try {
    // 初始化 AI 消息
    chatHistory.value.push({
      role: 'assistant',
      content: '<span class="typing-indicator">正在思考中...</span>'
    });
    const aiMessageIndex = chatHistory.value.length - 1;

    // 调用后端API
    const response = await fetchChatResponse(message.trim(), userInfo);
    let fullResponse = "";

    for await (const chunk of response) {
      fullResponse = chunk;
      // 实时渲染 Markdown 并添加光标
      const displayContent = marked.parse(fullResponse + ' ▌');
      chatHistory.value[aiMessageIndex].content = displayContent;
      await scrollToBottom();
    }

    // 最终渲染，移除光标
    chatHistory.value[aiMessageIndex].content = marked.parse(fullResponse);
    await nextTick();
    addCopyButtons();
    await scrollToBottom();

    ElMessage.success(`响应完成 (${fullResponse.length} 字符)`);

    // 保存当前会话
    saveCurrentSession();
    saveSessions();

  } catch (error) {
    console.error('发送消息失败:', error);

    // 移除失败的AI消息
    if (chatHistory.value.length > 0 && chatHistory.value[chatHistory.value.length - 1].role === 'assistant') {
      chatHistory.value.pop();
    }

    let errorMessage = '发送失败';
    if (error.message.includes('用户未登录')) {
      errorMessage = '用户未登录，请重新登录';
    } else if (error.message.includes('无法连接')) {
      errorMessage = 'GuanBao服务连接失败，请检查服务器状态';
      serverStatus.value = 'error';
    } else {
      errorMessage = `发送失败: ${error.message}`;
    }

    ElMessage.error(errorMessage);

  } finally {
    isLoading.value = false;
    isGenerating.value = false;
    abortController.value = null;
  }
};

// 流式聊天API调用
const fetchChatResponse = async function* (message, userInfo) {
  const API_URL = '/v1/chat/completions';
  const requestId = Date.now().toString() + Math.random().toString(36).substring(2, 9);

  const requestBody = {
    request_id: requestId,
    phone_number: userInfo.phoneNumber,
    query: message,
    api_key: userInfo.apiKey
  };

  console.log('发送GuanBao API请求:', {
    url: API_URL,
    requestId: requestId,
    phoneNumber: userInfo.phoneNumber,
    queryLength: message.length
  });

  try {
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: abortController.value?.signal
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let fullResponse = "";
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('流式响应完成，总长度:', fullResponse.length);
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        const lines = buffer.split('\n');
        buffer = lines.pop() || "";

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (!trimmedLine) continue;

          try {
            const json = JSON.parse(trimmedLine);

            if (json.error) {
              throw new Error(json.error);
            }

            if (json.request_id === requestId && json.phone_number === userInfo.phoneNumber) {
              const content = json.response || "";

              if (content) {
                fullResponse += content;
                yield fullResponse;
              } else if (json.response === "") {
                console.log('收到结束标记（空响应）');
                return fullResponse;
              }
            }

          } catch (parseError) {
            console.warn('解析JSON失败:', trimmedLine, parseError);
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return fullResponse;

  } catch (error) {
    console.error('API通信错误:', error);

    if (error.name === 'AbortError') {
      // 用户主动停止，不抛出错误
      console.log('用户主动停止生成');
      return '';
    } else if (error.message.includes('Failed to fetch')) {
      throw new Error('无法连接到服务器，请检查服务器是否正在运行');
    } else {
      throw error;
    }
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (scrollbarRef.value) {
    const scrollbar = scrollbarRef.value;
    scrollbar.setScrollTop(scrollbar.wrapRef.scrollHeight);
  }
};

// 添加代码复制按钮
const addCopyButtons = () => {
  nextTick(() => {
    const preElements = document.querySelectorAll('.chat-messages pre');
    preElements.forEach(pre => {
      if (!pre.querySelector('.copy-btn')) {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerText = '复制';
        copyBtn.onclick = () => {
          const code = pre.querySelector('code').innerText;
          navigator.clipboard.writeText(code).then(() => {
            copyBtn.innerText = '已复制!';
            setTimeout(() => { copyBtn.innerText = '复制'; }, 2000);
          });
        };
        pre.appendChild(copyBtn);
      }
    });
  });
};

// 发送建议
const sendSuggestion = (prompt) => {
  if (serverStatus.value !== 'healthy') {
    ElMessage.warning('请等待GuanBao服务连接成功后再试');
    return;
  }
  handleSend(prompt);
};

// 新建对话
const newChat = () => {
  // 如果当前有会话且有消息，保存当前会话
  if (currentSessionId.value && chatHistory.value.length > 0) {
    saveCurrentSession();
  }

  // 如果当前没有消息，直接清空即可，不创建新会话
  if (chatHistory.value.length === 0) {
    currentSessionId.value = null;
    messageInput.value = '';
    isLoading.value = false;
    return;
  }

  // 只有当前有消息时才创建新会话
  currentSessionId.value = null;
  chatHistory.value = [];
  messageInput.value = '';
  isLoading.value = false;
};

// 处理Shift+Enter
const handleShiftEnter = (e) => {
  return true;
};

// 停止生成
const stopGeneration = () => {
  if (abortController.value) {
    abortController.value.abort();
    abortController.value = null;
  }

  isLoading.value = false;
  isGenerating.value = false;

  // 如果有正在生成的消息，添加停止标记
  if (chatHistory.value.length > 0) {
    const lastMessage = chatHistory.value[chatHistory.value.length - 1];
    if (lastMessage.role === 'assistant') {
      // 移除打字机光标和临时内容
      let content = lastMessage.content;
      if (content.includes('▌')) {
        content = content.replace(' ▌', '');
      }
      if (content.includes('正在思考中...')) {
        content = '生成已停止';
      }
      lastMessage.content = marked.parse(content + '\n\n*[生成已停止]*');

      // 保存当前会话
      saveCurrentSession();
      saveSessions();
    }
  }

  ElMessage.info('已停止生成');
};

// 测试连接
const testConnection = async () => {
  await initializeConnection();
};

// 保存当前会话
const saveCurrentSession = () => {
  if (!currentSessionId.value) return;

  const sessionIndex = chatSessions.value.findIndex(s => s.id === currentSessionId.value);
  if (sessionIndex !== -1) {
    chatSessions.value[sessionIndex].messages = [...chatHistory.value];
    chatSessions.value[sessionIndex].updatedAt = new Date();

    // 生成会话标题（使用第一条用户消息）
    const firstUserMessage = chatHistory.value.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      const title = firstUserMessage.content.substring(0, 20) + (firstUserMessage.content.length > 20 ? '...' : '');
      chatSessions.value[sessionIndex].title = title;
    }

    // 按更新时间排序
    chatSessions.value.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
  }
};

// 切换到指定会话
const switchToSession = (sessionId) => {
  if (sessionId === currentSessionId.value) return;

  // 保存当前会话
  if (currentSessionId.value && chatHistory.value.length > 0) {
    saveCurrentSession();
  }

  // 切换到新会话
  const session = chatSessions.value.find(s => s.id === sessionId);
  if (session) {
    currentSessionId.value = sessionId;
    chatHistory.value = [...session.messages];
    messageInput.value = '';
    isLoading.value = false;

    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
  }
};

// 保存会话到localStorage
const saveSessions = () => {
  try {
    localStorage.setItem('chatSessions', JSON.stringify(chatSessions.value));
  } catch (error) {
    console.error('保存会话失败:', error);
  }
};

// 从localStorage加载会话
const loadSessions = () => {
  try {
    const saved = localStorage.getItem('chatSessions');
    if (saved) {
      const sessions = JSON.parse(saved);
      chatSessions.value = sessions.map(session => ({
        ...session,
        createdAt: new Date(session.createdAt),
        updatedAt: new Date(session.updatedAt)
      }));

      // 按更新时间排序
      chatSessions.value.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    }
  } catch (error) {
    console.error('加载会话失败:', error);
    chatSessions.value = [];
  }
};

// 删除会话
const deleteSession = (sessionId) => {
  ElMessageBox.confirm(
    '确定要删除这个会话吗？删除后无法恢复。',
    '删除会话',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从会话列表中移除
    const sessionIndex = chatSessions.value.findIndex(s => s.id === sessionId);
    if (sessionIndex !== -1) {
      chatSessions.value.splice(sessionIndex, 1);
    }

    // 如果删除的是当前会话，清空聊天记录
    if (currentSessionId.value === sessionId) {
      currentSessionId.value = null;
      chatHistory.value = [];
      messageInput.value = '';
      isLoading.value = false;
    }

    // 保存到localStorage
    saveSessions();

    ElMessage.success('会话已删除');
  }).catch(() => {
    // 用户取消删除
  });
};

// 格式化时间显示
const formatTime = (date) => {
  const now = new Date();
  const diff = now - new Date(date);
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;

  return new Date(date).toLocaleDateString();
};
</script>

<style>
:root {
  --sidebar-bg: #f7f7f8;
  --main-bg: #ffffff;
  --border-color: #e5e7eb;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --accent-color: #4f46e5;
  --accent-color-hover: #4338ca;
  --user-msg-bg: #eff6ff;
  --bot-msg-bg: #f9fafb;
  --code-bg: #111827;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  margin: 0;
  background-color: var(--main-bg);
  color: var(--text-primary);
  overflow: hidden;
}

.app-container {
  height: 100vh;
  width: 100vw;
}

/* 登录提示页面 */
.login-prompt {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-content {
  text-align: center;
  color: white;
  padding: 2rem;
}

.login-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 300;
}

.login-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* 聊天界面 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.chat-content {
  flex-grow: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 登录表单 */
.login-form {
  padding: 20px 0;
}

.form-actions {
  margin: 20px 0;
}

.tips {
  margin-top: 15px;
}

/* 聊天界面样式 */
.app-layout {
  height: 100vh;
  width: 100vw;
}

/* 侧边栏样式 */
.chat-sidebar {
  background-color: var(--sidebar-bg, #f7f7f8);
  height: 100vh;
  padding: 1rem;
  border-right: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  margin-bottom: 1.5rem;
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 1rem;
}

.chat-history {
  flex-grow: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.history-header {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary, #6b7280);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  margin-bottom: 0.5rem;
}

.history-list {
  padding: 0 0.5rem;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s;
  border: 1px solid transparent;
  position: relative;
}

.history-item:hover {
  background-color: rgba(79, 70, 229, 0.1);
  border-color: var(--accent-color, #4f46e5);
}

.history-item.active {
  background-color: var(--accent-color, #4f46e5);
  color: white;
}

.session-content {
  flex: 1;
  cursor: pointer;
  min-width: 0;
}

.session-title {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-time {
  font-size: 0.75rem;
  opacity: 0.7;
}

.history-item.active .session-time {
  opacity: 0.9;
}

.delete-btn {
  opacity: 0;
  transition: opacity 0.2s;
  color: var(--text-secondary, #6b7280);
  padding: 0.25rem;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.history-item:hover .delete-btn {
  opacity: 1;
}

.history-item.active .delete-btn {
  color: rgba(255, 255, 255, 0.8);
  opacity: 1;
}

.delete-btn:hover {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 4px;
}

.no-history {
  text-align: center;
  color: var(--text-secondary, #6b7280);
  font-size: 0.9rem;
  padding: 2rem 1rem;
}

.user-section {
  padding: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.5);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary, #6b7280);
}

.phone-number {
  font-size: 0.9rem;
  font-weight: 500;
}

.logout-btn {
  color: var(--text-secondary, #6b7280);
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s;
}

.logout-btn:hover {
  background-color: var(--border-color, #e5e7eb);
  color: var(--text-primary, #1f2937);
}

/* 主聊天区样式 */
.chat-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  padding: 0;
  overflow: hidden;
}

.debug-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 100;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.debug-btn:hover {
  opacity: 1;
}

/* 欢迎界面 */
.welcome-screen {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.logo {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--accent-color, #4f46e5);
  margin-bottom: 1rem;
}

.server-status {
  margin-bottom: 2rem;
  max-width: 600px;
}

.suggestion-cards {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 800px;
}

.suggestion-card {
  width: 180px;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
}

.suggestion-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.suggestion-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.suggestion-card.disabled:hover {
  transform: none;
  box-shadow: none;
}

.suggestion-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.suggestion-card p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

/* 聊天容器 */
.chat-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex-grow: 1;
  padding: 2rem 1rem;
}

.message-wrapper {
  display: flex;
  gap: 1rem;
  max-width: 85%;
  margin-bottom: 1.5rem;
}

.message-wrapper.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-wrapper.assistant {
  align-self: flex-start;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--accent-color, #4f46e5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  flex-shrink: 0;
}

.message-wrapper.user .avatar {
  background-color: #10b981;
}

.message {
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  line-height: 1.6;
}

.message-wrapper.user .message {
  background-color: var(--user-msg-bg, #eff6ff);
}

.message-wrapper.assistant .message {
  background-color: var(--bot-msg-bg, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
}

/* Markdown 样式 */
.message :deep(p:first-child) { margin-top: 0; }
.message :deep(p:last-child) { margin-bottom: 0; }
.message :deep(p) { margin: 0.5rem 0; line-height: 1.6; }

.message :deep(h1),
.message :deep(h2),
.message :deep(h3),
.message :deep(h4),
.message :deep(h5),
.message :deep(h6) {
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
  line-height: 1.4;
}

.message :deep(h1) { font-size: 1.5rem; }
.message :deep(h2) { font-size: 1.3rem; }
.message :deep(h3) { font-size: 1.1rem; }

.message :deep(ul),
.message :deep(ol) {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.message :deep(li) {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.message :deep(blockquote) {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid var(--accent-color, #4f46e5);
  background-color: rgba(79, 70, 229, 0.05);
  font-style: italic;
}

.message :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.message :deep(th),
.message :deep(td) {
  border: 1px solid var(--border-color, #e5e7eb);
  padding: 0.5rem;
  text-align: left;
}

.message :deep(th) {
  background-color: var(--sidebar-bg, #f7f7f8);
  font-weight: 600;
}

.message :deep(pre) {
  position: relative;
  background-color: var(--code-bg, #111827);
  color: #d1d5db;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1rem 0;
}

.message :deep(code) {
  font-family: 'SF Mono', 'Fira Code', 'Menlo', monospace;
  font-size: 0.9em;
}

.message :deep(p code),
.message :deep(li code) {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--accent-color, #4f46e5);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.85em;
}

.message :deep(a) {
  color: var(--accent-color, #4f46e5);
  text-decoration: none;
}

.message :deep(a:hover) {
  text-decoration: underline;
}

.message :deep(hr) {
  border: none;
  border-top: 1px solid var(--border-color, #e5e7eb);
  margin: 1.5rem 0;
}

.message :deep(.copy-btn) {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #374151;
  color: #d1d5db;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.message :deep(pre:hover .copy-btn) {
  opacity: 1;
}

.typing-indicator {
  color: var(--text-secondary, #6b7280);
  font-style: italic;
}

/* 输入区样式 */
.chat-input-area {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--main-bg, #ffffff);
}

.input-wrapper {
  position: relative;
  width: calc(100% - 4rem);
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  align-items: flex-end;
  gap: 0.5rem;
}

.chat-input {
  flex: 1;
}

.chat-input :deep(.el-textarea__inner) {
  padding: 1rem 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color, #e5e7eb);
  font-size: 1rem;
  resize: none;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s;
}

.chat-input :deep(.el-textarea__inner:focus) {
  border-color: var(--accent-color, #4f46e5);
}

.send-button {
  width: 40px;
  height: 40px;
  background-color: var(--accent-color, #4f46e5);
  border-color: var(--accent-color, #4f46e5);
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: var(--accent-color-hover, #4338ca);
  border-color: var(--accent-color-hover, #4338ca);
}

.send-button:disabled {
  background-color: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
}

.stop-button {
  width: 40px;
  height: 40px;
  background-color: #ef4444;
  border-color: #ef4444;
  transition: background-color 0.2s;
  animation: pulse 2s infinite;
}

.stop-button:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 调试面板 */
.debug-content {
  padding: 1rem;
}
</style>
