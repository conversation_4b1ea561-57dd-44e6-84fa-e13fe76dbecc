<template>
  <el-main class="chat-main">
    <!-- 调试按钮 -->
    <el-button
      class="debug-btn"
      type="info"
      size="small"
      circle
      @click="showDebugPanel = true"
      title="API调试面板"
    >
      <el-icon><Setting /></el-icon>
    </el-button>

    <!-- 欢迎界面 -->
    <div v-if="!chatHistory.length" class="welcome-screen">
      <div class="logo">AI Chat Pro</div>

      <!-- 服务器状态显示 -->
      <div class="server-status">
        <el-alert
          v-if="serverStatus === 'healthy'"
          title="GuanBao 服务已连接"
          :description="`用户: ${currentUser}`"
          type="success"
          :closable="false"
          show-icon
        />
        <el-alert
          v-else-if="serverStatus === 'error'"
          title="GuanBao 服务连接失败"
          description="请检查后端服务器是否正在运行在 http://************:82"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请检查后端服务器是否正在运行在 http://************:82</p>
            <el-button size="small" type="primary" @click="initializeConnection">
              重新连接
            </el-button>
          </template>
        </el-alert>
        <el-alert
          v-else
          title="正在连接 GuanBao 服务..."
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <div class="suggestion-cards">
        <el-card
          v-for="(card, index) in suggestionCards"
          :key="index"
          @click="sendSuggestion(card.prompt)"
          class="suggestion-card"
          shadow="hover"
          :class="{ disabled: serverStatus !== 'healthy' }"
        >
          <h3>{{ card.title }}</h3>
          <p>{{ card.prompt }}</p>
        </el-card>
      </div>
    </div>

    <!-- 聊天记录区 -->
    <div v-else class="chat-container">
      <el-scrollbar ref="scrollbarRef" class="chat-messages">
        <div v-for="(message, index) in chatHistory" :key="index" class="message-wrapper" :class="message.role">
          <div class="avatar">{{ message.role === 'user' ? 'U' : 'AI' }}</div>
          <div class="message" v-html="message.content"></div>
        </div>
      </el-scrollbar>
    </div>

    <ChatInput @send="handleSend" :loading="isLoading" />

    <!-- API调试面板 -->
    <ApiDebugPanel v-model="showDebugPanel" />
  </el-main>
</template>

<script setup>
import { ref, nextTick, onMounted, watch } from 'vue';
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
import { Setting } from '@element-plus/icons-vue';
import ChatInput from './ChatInput.vue';
import ApiDebugPanel from './ApiDebugPanel.vue';
import {
  fetchChatResponse,
  checkServerHealth,
  testChatCompletion,
  getApiConfig
} from '../utils/api.js';
import { getCurrentUser } from '../utils/authApi.js';
import { ElMessage, ElNotification } from 'element-plus';

// 响应式数据
const chatHistory = ref([]);
const isLoading = ref(false);
const scrollbarRef = ref(null);
const serverStatus = ref('unknown'); // unknown, healthy, error
const currentUser = ref('');
const connectionTested = ref(false);
const showDebugPanel = ref(false);

// 建议卡片数据
const suggestionCards = ref([
  { title: '解释概念', prompt: '用一句话总结一下什么是量子计算' },
  { title: '写商务邮件', prompt: '帮我写一封商务邮件，主题是项目延期通知' },
  { title: '编写代码', prompt: '写一个Python函数，实现快速排序算法' },
  { title: '提供建议', prompt: '我应该如何开始学习一门新的语言？' }
]);

// 配置 marked 和 highlight.js
onMounted(async () => {
  marked.setOptions({
    highlight: function(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, { language }).value;
    },
    langPrefix: 'hljs language-',
  });

  // 初始化时检查服务器状态
  await initializeConnection();
});

// 初始化连接
const initializeConnection = async () => {
  try {
    console.log('正在检查GuanBao后端服务连接...');

    // 获取用户信息
    const userInfo = getCurrentUser();
    if (!userInfo) {
      serverStatus.value = 'error';
      ElNotification({
        title: '用户未登录',
        message: '请先登录后再使用聊天功能',
        type: 'warning',
        duration: 5000
      });
      return;
    }

    currentUser.value = userInfo.phoneNumber;

    // 检查服务器健康状态
    const isHealthy = await checkServerHealth();

    if (isHealthy) {
      serverStatus.value = 'healthy';

      try {
        // 进行简单的连接测试
        await testChatCompletion("测试连接");
        connectionTested.value = true;

        ElNotification({
          title: '连接成功',
          message: `已成功连接到GuanBao服务，用户: ${userInfo.phoneNumber}`,
          type: 'success',
          duration: 3000
        });

      } catch (testError) {
        console.warn('连接测试失败:', testError);
        ElNotification({
          title: '连接警告',
          message: '服务器运行正常，但聊天功能可能不可用',
          type: 'warning',
          duration: 4000
        });
      }

    } else {
      serverStatus.value = 'error';
      ElNotification({
        title: '连接失败',
        message: 'GuanBao后端服务不可用，请检查服务器是否正在运行',
        type: 'error',
        duration: 0 // 不自动关闭
      });
    }

  } catch (error) {
    console.error('初始化连接失败:', error);
    serverStatus.value = 'error';
    ElNotification({
      title: '连接错误',
      message: `无法连接到GuanBao服务: ${error.message}`,
      type: 'error',
      duration: 0
    });
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (scrollbarRef.value) {
    const scrollbar = scrollbarRef.value;
    scrollbar.setScrollTop(scrollbar.wrapRef.scrollHeight);
  }
};

// 处理发送消息
const handleSend = async (message) => {
  // 检查用户登录状态
  const userInfo = getCurrentUser();
  if (!userInfo) {
    ElMessage.error('用户未登录，请先登录');
    return;
  }

  // 检查服务器状态
  if (serverStatus.value === 'error') {
    ElMessage.error('GuanBao服务不可用，请检查服务器连接');
    return;
  }

  // 添加用户消息
  chatHistory.value.push({ role: 'user', content: message });
  isLoading.value = true;

  await scrollToBottom();

  // 准备消息历史，清理HTML标签
  const messages = chatHistory.value.map(({ role, content }) => ({
    role,
    content: typeof content === 'string' ? content.replace(/<[^>]*>/g, '') : content
  }));

  console.log('发送消息到GuanBao:', {
    user: userInfo.phoneNumber,
    messageCount: messages.length,
    lastMessage: message.substring(0, 100) + (message.length > 100 ? '...' : '')
  });

  // 初始化 AI 消息
  chatHistory.value.push({
    role: 'assistant',
    content: '<span class="typing-indicator">正在思考中...</span>'
  });
  const aiMessageIndex = chatHistory.value.length - 1;

  try {
    const responseStream = fetchChatResponse(messages);
    let fullResponse = "";
    let chunkCount = 0;
    const startTime = Date.now();

    for await (const chunk of responseStream) {
      chunkCount++;
      fullResponse = chunk;

      // 实时渲染 Markdown 并添加光标
      const displayContent = marked.parse(fullResponse + ' ▌');
      chatHistory.value[aiMessageIndex].content = displayContent;
      await scrollToBottom();

      // 每50个chunk输出一次日志
      if (chunkCount % 50 === 0) {
        console.log(`已接收 ${chunkCount} 个数据块，当前长度: ${fullResponse.length}`);
      }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('流式响应完成:', {
      totalChunks: chunkCount,
      responseLength: fullResponse.length,
      duration: `${duration.toFixed(2)}s`,
      avgSpeed: `${(fullResponse.length / duration).toFixed(0)} 字符/秒`
    });

    // 最终渲染，移除光标并添加代码复制按钮
    chatHistory.value[aiMessageIndex].content = marked.parse(fullResponse);
    await nextTick();
    addCopyButtons();
    await scrollToBottom();

    // 显示成功提示
    if (fullResponse.length > 0) {
      ElMessage.success(`响应完成 (${fullResponse.length} 字符，耗时 ${duration.toFixed(1)}s)`);
    }

  } catch (error) {
    console.error('发送消息失败:', error);

    // 移除失败的AI消息
    if (chatHistory.value.length > 0 && chatHistory.value[chatHistory.value.length - 1].role === 'assistant') {
      chatHistory.value.pop();
    }

    // 根据错误类型显示不同的提示
    let errorMessage = '发送失败';
    if (error.message.includes('用户未登录')) {
      errorMessage = '用户未登录，请重新登录';
    } else if (error.message.includes('无法连接')) {
      errorMessage = 'GuanBao服务连接失败，请检查服务器状态';
      serverStatus.value = 'error';
    } else if (error.message.includes('超时')) {
      errorMessage = '请求超时，请稍后重试';
    } else {
      errorMessage = `发送失败: ${error.message}`;
    }

    ElMessage.error(errorMessage);

    // 提供重试选项
    ElNotification({
      title: '发送失败',
      message: `${errorMessage}\n点击重新检查连接`,
      type: 'error',
      duration: 5000,
      onClick: () => {
        initializeConnection();
      }
    });

  } finally {
    isLoading.value = false;
  }
};

// 添加代码复制按钮
const addCopyButtons = () => {
  nextTick(() => {
    const preElements = document.querySelectorAll('.chat-messages pre');
    preElements.forEach(pre => {
      if (!pre.querySelector('.copy-btn')) {
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerText = '复制';
        copyBtn.onclick = () => {
          const code = pre.querySelector('code').innerText;
          navigator.clipboard.writeText(code).then(() => {
            copyBtn.innerText = '已复制!';
            setTimeout(() => { copyBtn.innerText = '复制'; }, 2000);
          });
        };
        pre.appendChild(copyBtn);
      }
    });
  });
};

// 发送建议
const sendSuggestion = (prompt) => {
  if (serverStatus.value !== 'healthy') {
    ElMessage.warning('请等待GuanBao服务连接成功后再试');
    return;
  }
  handleSend(prompt);
};

// 重置聊天
const resetChat = () => {
  chatHistory.value = [];
  isLoading.value = false;
};

// 暴露方法给父组件
defineExpose({
  resetChat
});
</script>

<style scoped>
.chat-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

.debug-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 100;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.debug-btn:hover {
  opacity: 1;
}

/* 欢迎界面 */
.welcome-screen {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.logo {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--accent-color, #4f46e5);
  margin-bottom: 1rem;
}

.server-status {
  margin-bottom: 2rem;
  max-width: 600px;
}

.suggestion-cards {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 800px;
}

.suggestion-card {
  width: 180px;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
}

.suggestion-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.suggestion-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.suggestion-card.disabled:hover {
  transform: none;
  box-shadow: none;
}

.suggestion-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.suggestion-card p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

/* 聊天容器 */
.chat-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex-grow: 1;
  padding: 2rem 1rem;
}

.message-wrapper {
  display: flex;
  gap: 1rem;
  max-width: 85%;
  margin-bottom: 1.5rem;
}

.message-wrapper.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-wrapper.assistant {
  align-self: flex-start;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--accent-color, #4f46e5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  flex-shrink: 0;
}

.message-wrapper.user .avatar {
  background-color: #10b981;
}

.message {
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  line-height: 1.6;
}

.message-wrapper.user .message {
  background-color: var(--user-msg-bg, #eff6ff);
}

.message-wrapper.assistant .message {
  background-color: var(--bot-msg-bg, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
}

/* Markdown 样式 */
.message :deep(p:first-child) { margin-top: 0; }
.message :deep(p:last-child) { margin-bottom: 0; }

.message :deep(pre) {
  position: relative;
  background-color: var(--code-bg, #111827);
  color: #d1d5db;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
}

.message :deep(code) {
  font-family: 'SF Mono', 'Fira Code', 'Menlo', monospace;
  font-size: 0.9em;
}

.message :deep(.copy-btn) {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #374151;
  color: #d1d5db;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.message :deep(pre:hover .copy-btn) {
  opacity: 1;
}

.typing-indicator {
  color: var(--text-secondary, #6b7280);
  font-style: italic;
}
</style>